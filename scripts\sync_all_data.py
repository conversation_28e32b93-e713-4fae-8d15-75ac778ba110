#!/usr/bin/env python3
"""
Synchronize All AutoQA Data Sources
Fix synchronization issues between MongoDB, Pinecone, and MySQL
"""

import sys
import os
import json
from pathlib import Path
from typing import Dict, List, Any, Set

# Add src to path and load environment
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root / "src"))

from dotenv import load_dotenv
load_dotenv(project_root / ".env", override=True)

from database.connections import DatabaseConnections

class DataSynchronizer:
    """Synchronize data across all AutoQA systems"""
    
    def __init__(self):
        self.db_connections = DatabaseConnections()
    
    def sync_pinecone_to_mysql(self) -> Dict[str, Any]:
        """Sync all Pinecone QA results to MySQL"""
        print("🔄 SYNCING PINECONE QA RESULTS TO MYSQL")
        print("=" * 50)
        
        try:
            # Get all QA results from Pinecone
            qa_results_index = self.db_connections.get_pinecone_index('autoqa-qa-results')
            
            query_result = qa_results_index.query(
                vector=[0.0] * 1536,
                top_k=10000,
                include_metadata=True,
                filter={'autoqa_managed': True}
            )
            
            print(f"📊 Found {len(query_result.matches)} QA results in Pinecone")
            
            # Connect to MySQL
            mysql_conn = self.db_connections.connect_mysql()
            cursor = mysql_conn.cursor()
            
            synced_count = 0
            error_count = 0
            
            for match in query_result.matches:
                metadata = match.metadata
                call_id = metadata.get('call_id')
                
                if not call_id:
                    continue
                
                try:
                    # Prepare MySQL data
                    mysql_data = {
                        'call_id': call_id,
                        'folder_id': metadata.get('folder_id'),
                        'csat': metadata.get('csat'),
                        'issue_resolved': metadata.get('issue_resolved'),
                        'call_verdict': metadata.get('call_verdict'),
                        'call_topic': metadata.get('call_topic'),
                        'overall_score_percentage': metadata.get('overall_score_percentage'),
                        'analyzed_at': metadata.get('analyzed_at'),
                        'analysis_method': metadata.get('analysis_method', 'autoqa_parallel'),
                        'openai_model': metadata.get('openai_model', 'gpt-4o-mini'),
                        'full_qa_result_json': metadata.get('full_qa_result_json'),
                        'assessment_table_json': metadata.get('assessment_table_json'),
                        'call_summary_json': metadata.get('call_summary_json'),
                        'agent_feedback_json': metadata.get('agent_feedback_json'),
                        'scores_json': metadata.get('scores_json')
                    }
                    
                    # Insert or update in MySQL
                    insert_query = """
                    INSERT INTO qa_results (
                        call_id, folder_id, csat, issue_resolved, call_verdict, call_topic,
                        overall_score_percentage, analyzed_at, analysis_method, openai_model,
                        full_qa_result_json, assessment_table_json, call_summary_json,
                        agent_feedback_json, scores_json, status
                    ) VALUES (
                        %(call_id)s, %(folder_id)s, %(csat)s, %(issue_resolved)s, %(call_verdict)s, %(call_topic)s,
                        %(overall_score_percentage)s, %(analyzed_at)s, %(analysis_method)s, %(openai_model)s,
                        %(full_qa_result_json)s, %(assessment_table_json)s, %(call_summary_json)s,
                        %(agent_feedback_json)s, %(scores_json)s, 'completed'
                    ) ON DUPLICATE KEY UPDATE
                        folder_id = VALUES(folder_id),
                        csat = VALUES(csat),
                        issue_resolved = VALUES(issue_resolved),
                        call_verdict = VALUES(call_verdict),
                        call_topic = VALUES(call_topic),
                        overall_score_percentage = VALUES(overall_score_percentage),
                        analyzed_at = VALUES(analyzed_at),
                        analysis_method = VALUES(analysis_method),
                        openai_model = VALUES(openai_model),
                        full_qa_result_json = VALUES(full_qa_result_json),
                        assessment_table_json = VALUES(assessment_table_json),
                        call_summary_json = VALUES(call_summary_json),
                        agent_feedback_json = VALUES(agent_feedback_json),
                        scores_json = VALUES(scores_json),
                        status = 'completed',
                        processed_at = CURRENT_TIMESTAMP
                    """
                    
                    cursor.execute(insert_query, mysql_data)
                    synced_count += 1
                    
                    if synced_count % 10 == 0:
                        print(f"✅ Synced {synced_count} records...")
                    
                except Exception as e:
                    print(f"❌ Error syncing {call_id}: {e}")
                    error_count += 1
            
            mysql_conn.commit()
            cursor.close()
            
            print(f"🎉 Sync Complete!")
            print(f"   Synced: {synced_count}")
            print(f"   Errors: {error_count}")
            
            return {
                'synced': synced_count,
                'errors': error_count,
                'success': True
            }
            
        except Exception as e:
            print(f"❌ Sync failed: {e}")
            return {'success': False, 'error': str(e)}
    
    def clean_mysql_duplicates(self) -> Dict[str, Any]:
        """Clean duplicate records in MySQL"""
        print("\n🧹 CLEANING MYSQL DUPLICATES")
        print("=" * 50)
        
        try:
            mysql_conn = self.db_connections.connect_mysql()
            cursor = mysql_conn.cursor()
            
            # Find duplicates
            cursor.execute("""
                SELECT call_id, COUNT(*) as count 
                FROM qa_results 
                GROUP BY call_id 
                HAVING COUNT(*) > 1
            """)
            
            duplicates = cursor.fetchall()
            
            if not duplicates:
                print("✅ No duplicates found")
                cursor.close()
                return {'cleaned': 0, 'success': True}
            
            print(f"🔍 Found {len(duplicates)} duplicate call_ids")
            
            cleaned_count = 0
            
            for call_id, count in duplicates:
                # Keep the most recent record, delete others
                cursor.execute("""
                    DELETE FROM qa_results 
                    WHERE call_id = %s 
                    AND id NOT IN (
                        SELECT * FROM (
                            SELECT id FROM qa_results 
                            WHERE call_id = %s 
                            ORDER BY processed_at DESC 
                            LIMIT 1
                        ) as temp
                    )
                """, (call_id, call_id))
                
                cleaned_count += cursor.rowcount
                print(f"🧹 Cleaned {cursor.rowcount} duplicates for {call_id}")
            
            mysql_conn.commit()
            cursor.close()
            
            print(f"✅ Cleaned {cleaned_count} duplicate records")
            
            return {
                'cleaned': cleaned_count,
                'success': True
            }
            
        except Exception as e:
            print(f"❌ Cleanup failed: {e}")
            return {'success': False, 'error': str(e)}
    
    def reset_demo_records(self, count: int = 10) -> Dict[str, Any]:
        """Reset some processed records for demo"""
        print(f"\n🔄 RESETTING {count} RECORDS FOR DEMO")
        print("=" * 50)
        
        try:
            # Get Pinecone transcripts index
            transcripts_index = self.db_connections.get_pinecone_index('autoqa-transcripts')
            
            # Find processed records
            query_result = transcripts_index.query(
                vector=[0.0] * 1536,
                top_k=1000,
                include_metadata=True,
                filter={'autoqa_managed': True, 'qa_status': 'processed'}
            )
            
            processed_records = query_result.matches
            
            if len(processed_records) < count:
                count = len(processed_records)
            
            if count == 0:
                print("⚠️ No processed records found to reset")
                return {'reset': 0, 'success': True}
            
            print(f"🔍 Found {len(processed_records)} processed records")
            print(f"🔄 Resetting {count} records...")
            
            reset_count = 0
            
            for i in range(count):
                match = processed_records[i]
                vector_id = match.id
                metadata = match.metadata.copy()
                call_id = metadata.get('call_id')
                
                # Update metadata to mark as unprocessed
                metadata['qa_status'] = 'unprocessed'
                
                # Remove processing-related metadata
                metadata.pop('qa_analyzed_at', None)
                metadata.pop('qa_score', None)
                metadata.pop('qa_method', None)
                
                # Update the vector in Pinecone
                transcripts_index.update(
                    id=vector_id,
                    set_metadata=metadata
                )
                
                print(f"✅ Reset {call_id} to unprocessed")
                reset_count += 1
            
            print(f"🎉 Reset {reset_count} records for demo!")
            
            return {
                'reset': reset_count,
                'success': True
            }
            
        except Exception as e:
            print(f"❌ Reset failed: {e}")
            return {'success': False, 'error': str(e)}
    
    def verify_synchronization(self) -> Dict[str, Any]:
        """Verify that all systems are synchronized"""
        print("\n✅ VERIFYING SYNCHRONIZATION")
        print("=" * 50)
        
        try:
            # Count records in each system
            mysql_conn = self.db_connections.connect_mysql()
            cursor = mysql_conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM qa_results")
            mysql_count = cursor.fetchone()[0]
            cursor.close()
            
            # Count Pinecone QA results
            qa_results_index = self.db_connections.get_pinecone_index('autoqa-qa-results')
            qa_query = qa_results_index.query(
                vector=[0.0] * 1536,
                top_k=10000,
                include_metadata=True,
                filter={'autoqa_managed': True}
            )
            pinecone_qa_count = len(qa_query.matches)
            
            # Count unprocessed records
            transcripts_index = self.db_connections.get_pinecone_index('autoqa-transcripts')
            transcript_query = transcripts_index.query(
                vector=[0.0] * 1536,
                top_k=10000,
                include_metadata=True,
                filter={'autoqa_managed': True, 'qa_status': 'unprocessed'}
            )
            unprocessed_count = len(transcript_query.matches)
            
            verification = {
                'mysql_qa_results': mysql_count,
                'pinecone_qa_results': pinecone_qa_count,
                'unprocessed_records': unprocessed_count,
                'synchronized': mysql_count == pinecone_qa_count,
                'demo_ready': unprocessed_count > 0
            }
            
            print(f"📊 Verification Results:")
            print(f"   MySQL QA Results: {mysql_count}")
            print(f"   Pinecone QA Results: {pinecone_qa_count}")
            print(f"   Unprocessed Records: {unprocessed_count}")
            print(f"   Synchronized: {'✅ Yes' if verification['synchronized'] else '❌ No'}")
            print(f"   Demo Ready: {'✅ Yes' if verification['demo_ready'] else '❌ No'}")
            
            return verification
            
        except Exception as e:
            print(f"❌ Verification failed: {e}")
            return {'success': False, 'error': str(e)}
    
    def run_complete_sync(self) -> Dict[str, Any]:
        """Run complete synchronization process"""
        print("🔄 AUTOQA COMPLETE DATA SYNCHRONIZATION")
        print("=" * 60)
        
        results = {}
        
        # Step 1: Clean MySQL duplicates
        results['cleanup'] = self.clean_mysql_duplicates()
        
        # Step 2: Sync Pinecone to MySQL
        results['sync'] = self.sync_pinecone_to_mysql()
        
        # Step 3: Reset some records for demo
        results['reset'] = self.reset_demo_records(10)
        
        # Step 4: Verify synchronization
        results['verification'] = self.verify_synchronization()
        
        return results
    
    def close_connections(self):
        """Close all database connections"""
        self.db_connections.close_connections()

def main():
    """Main synchronization function"""
    synchronizer = DataSynchronizer()
    
    try:
        results = synchronizer.run_complete_sync()
        
        print("\n" + "=" * 60)
        print("🎯 SYNCHRONIZATION SUMMARY")
        print("=" * 60)
        
        verification = results.get('verification', {})
        
        if verification.get('synchronized') and verification.get('demo_ready'):
            print("🎉 SYNCHRONIZATION SUCCESSFUL!")
            print("✅ All systems are synchronized")
            print("✅ Demo records are available")
            print("\n🚀 Ready for demo!")
            print("   1. Restart web server: python web/qa_results_server.py")
            print("   2. Open browser: http://localhost:5000")
            print("   3. Process records using web interface")
            return 0
        else:
            print("⚠️ SYNCHRONIZATION ISSUES REMAIN")
            if not verification.get('synchronized'):
                print("❌ MySQL and Pinecone are not synchronized")
            if not verification.get('demo_ready'):
                print("❌ No unprocessed records available for demo")
            return 1
        
    except Exception as e:
        print(f"❌ Synchronization failed: {e}")
        return 1
    
    finally:
        synchronizer.close_connections()

if __name__ == "__main__":
    sys.exit(main())
