#!/usr/bin/env python3
"""
Simple Flask server to serve QA results
Basic API for viewing AutoQA analysis results
"""

import sys
import os
from pathlib import Path
from flask import Flask, jsonify, send_from_directory, request
from flask_cors import CORS
import json
from datetime import datetime

# Add src to path and load environment
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root / "src"))

from dotenv import load_dotenv
load_dotenv(project_root / ".env", override=True)

from database.connections import DatabaseConnections

# Import the AutoQAParallelProcessor for processing records
sys.path.append(str(project_root / "scripts"))
from autoqa_parallel_processor import AutoQAParallelProcessor

app = Flask(__name__)
CORS(app)  # Enable CORS for all routes

@app.route('/')
def index():
    """Serve the main HTML page"""
    return send_from_directory('.', 'qa_results_viewer.html')

@app.route('/api/qa-results')
def get_qa_results():
    """Get QA analysis results from MongoDB and Pinecone"""
    
    try:
        db_connections = DatabaseConnections()
        
        # Get QA results from MongoDB
        qa_collection = db_connections.get_mongodb_collection('qa_analysis')
        qa_results = []
        
        for doc in qa_collection.find().sort('analyzed_at', -1):
            # Convert ObjectId to string and clean up the document
            # Handle both old format and new comprehensive format

            # Convert ObjectId to string if present
            if '_id' in doc:
                doc['_id'] = str(doc['_id'])

            result = {
                'call_id': doc.get('call_id'),
                'folder_id': doc.get('folder_id'),
                'analyzed_at': doc.get('analyzed_at'),
                'analysis_method': doc.get('analysis_method'),
                'transcript_length': doc.get('transcript_length'),

                # Comprehensive QA fields
                'CSAT': doc.get('CSAT'),
                'Issue Resolved': doc.get('Issue Resolved'),
                'Call Verdict': doc.get('Call Verdict'),
                'Call Topic': doc.get('Call Topic'),
                'Call Topic L2': doc.get('Call Topic L2'),
                'Sentiment Shift': doc.get('Sentiment Shift'),
                'Non-Disclosure Statement': doc.get('Non-Disclosure Statement'),
                'Protocol Deviation': doc.get('Protocol Deviation'),
                'Auto-Fail': doc.get('Auto-Fail'),
                'Call Summary': doc.get('Call Summary'),
                'Assessment Table': doc.get('Assessment Table'),
                'Scores': doc.get('Scores'),

                # Extract overall score
                'overall_score_numeric': doc.get('overall_score_numeric', 0),

                # Legacy fields for backward compatibility
                'qa_score': doc.get('overall_score_numeric') or doc.get('call_quality_score', 0),
                'agent_performance': doc.get('agent_performance'),
                'customer_satisfaction': doc.get('customer_satisfaction'),
                'compliance_issues': doc.get('compliance_issues'),
                'key_topics': doc.get('key_topics'),
                'sentiment_analysis': doc.get('sentiment_analysis'),
                'recommendations': doc.get('recommendations'),
                'analysis_summary': doc.get('analysis_summary')
            }
            qa_results.append(result)
        
        # Get AutoQA Pinecone data from both indexes
        autoqa_transcripts = []
        autoqa_qa_results = []
        pinecone_error = None

        try:
            # Get data from autoqa-transcripts index
            transcripts_index = db_connections.get_pinecone_index('autoqa-transcripts')
            transcripts_query = transcripts_index.query(
                vector=[0.0] * 1536,
                top_k=100,
                include_metadata=True,
                filter={'autoqa_managed': True}
            )

            for match in transcripts_query.matches:
                metadata = match.metadata
                transcript_data = {
                    'call_id': metadata.get('call_id'),
                    'qa_analyzed': metadata.get('qa_analyzed', False),
                    'qa_score': metadata.get('qa_score'),
                    'qa_method': metadata.get('qa_method'),
                    'transcript_length': metadata.get('transcript_length'),
                    'folder_id': metadata.get('folder_id'),
                    'conversation_segments': metadata.get('conversation_segments'),
                    'migrated_at': metadata.get('migrated_at'),
                    'source': metadata.get('source'),
                    'has_transcript_data': bool(metadata.get('tran_text')),
                    'has_diarized_data': any([
                        metadata.get('agent_diarize_transcript'),
                        metadata.get('client_diarize_transcript'),
                        metadata.get('speaker_diarize_transcript')
                    ])
                }
                autoqa_transcripts.append(transcript_data)

            # Get data from autoqa-qa-results index
            qa_results_index = db_connections.get_pinecone_index('autoqa-qa-results')
            qa_query = qa_results_index.query(
                vector=[0.0] * 1536,
                top_k=100,
                include_metadata=True
            )

            for match in qa_query.matches:
                metadata = match.metadata
                qa_data = {
                    'call_id': metadata.get('call_id'),
                    'csat': metadata.get('csat'),
                    'call_verdict': metadata.get('call_verdict'),
                    'issue_resolved': metadata.get('issue_resolved'),
                    'call_topic': metadata.get('call_topic'),
                    'overall_score_percentage': metadata.get('overall_score_percentage'),
                    'analyzed_at': metadata.get('analyzed_at'),
                    'analysis_method': metadata.get('analysis_method'),
                    'folder_id': metadata.get('folder_id'),
                    'has_full_qa_json': bool(metadata.get('full_qa_result_json')),
                    'has_assessment_table': bool(metadata.get('assessment_table_json')),
                    'has_agent_feedback': bool(metadata.get('agent_feedback_json')),
                    'qa_status': metadata.get('qa_status', 'unknown')
                }
                autoqa_qa_results.append(qa_data)

        except Exception as pinecone_err:
            pinecone_error = str(pinecone_err)
            print(f"⚠️ AutoQA Pinecone indexes unavailable: {pinecone_error}")
            # Continue without Pinecone data
        
        # Calculate summary statistics
        total_count = len(qa_results)
        avg_score = 0

        if total_count > 0:
            scores = [float(r['qa_score']) for r in qa_results if r['qa_score'] and str(r['qa_score']).replace('.', '').isdigit()]
            avg_score = round(sum(scores) / len(scores), 1) if scores else 0

        # Calculate AutoQA statistics
        autoqa_avg_score = 0
        if autoqa_qa_results:
            autoqa_scores = [float(r['overall_score_percentage']) for r in autoqa_qa_results if r['overall_score_percentage']]
            autoqa_avg_score = round(sum(autoqa_scores) / len(autoqa_scores), 1) if autoqa_scores else 0

        summary = {
            'total_count': total_count,
            'avg_score': avg_score,
            'autoqa_transcripts': len(autoqa_transcripts),
            'autoqa_qa_results': len(autoqa_qa_results),
            'autoqa_avg_score': autoqa_avg_score,
            'analyzed_vectors': len([s for s in autoqa_transcripts if s['qa_analyzed']]),
            'pinecone_status': 'available' if not pinecone_error else f'unavailable ({pinecone_error})'
        }

        db_connections.close_connections()

        return jsonify({
            'success': True,
            'summary': summary,
            'qa_results': qa_results,
            'autoqa_transcripts': autoqa_transcripts,
            'autoqa_qa_results': autoqa_qa_results,
            'timestamp': datetime.now().isoformat()
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

def convert_objectid_to_string(obj):
    """Recursively convert ObjectId to string in nested dictionaries and lists"""
    from bson import ObjectId

    if isinstance(obj, ObjectId):
        return str(obj)
    elif isinstance(obj, dict):
        return {key: convert_objectid_to_string(value) for key, value in obj.items()}
    elif isinstance(obj, list):
        return [convert_objectid_to_string(item) for item in obj]
    else:
        return obj

@app.route('/api/call/<call_id>')
def get_call_details(call_id):
    """Get detailed information for a specific call"""

    try:
        db_connections = DatabaseConnections()

        # Get QA analysis
        qa_collection = db_connections.get_mongodb_collection('qa_analysis')
        qa_doc = qa_collection.find_one({'call_id': call_id})

        # Get original transcript
        transcript_collection = db_connections.get_mongodb_collection('call_smart_speech_transcribe')
        transcript_doc = transcript_collection.find_one({'call_id': call_id})

        # Get AutoQA Pinecone data for this specific call
        autoqa_transcript_data = None
        autoqa_qa_data = None

        try:
            # Get transcript data from autoqa-transcripts index
            transcripts_index = db_connections.get_pinecone_index('autoqa-transcripts')
            transcript_query = transcripts_index.query(
                vector=[0.0] * 1536,
                filter={'call_id': call_id},
                top_k=1,
                include_metadata=True
            )

            if transcript_query.matches:
                autoqa_transcript_data = transcript_query.matches[0].metadata

            # Get QA results data from autoqa-qa-results index
            qa_results_index = db_connections.get_pinecone_index('autoqa-qa-results')
            qa_query = qa_results_index.query(
                vector=[0.0] * 1536,
                filter={'call_id': call_id},
                top_k=1,
                include_metadata=True
            )

            if qa_query.matches:
                autoqa_qa_data = qa_query.matches[0].metadata

                # Parse JSON fields for display
                if autoqa_qa_data.get('full_qa_result_json'):
                    try:
                        autoqa_qa_data['full_qa_result_parsed'] = json.loads(autoqa_qa_data['full_qa_result_json'])
                    except json.JSONDecodeError:
                        autoqa_qa_data['full_qa_result_parsed'] = None

                if autoqa_qa_data.get('assessment_table_json'):
                    try:
                        autoqa_qa_data['assessment_table_parsed'] = json.loads(autoqa_qa_data['assessment_table_json'])
                    except json.JSONDecodeError:
                        autoqa_qa_data['assessment_table_parsed'] = None

                if autoqa_qa_data.get('agent_feedback_json'):
                    try:
                        autoqa_qa_data['agent_feedback_parsed'] = json.loads(autoqa_qa_data['agent_feedback_json'])
                    except json.JSONDecodeError:
                        autoqa_qa_data['agent_feedback_parsed'] = None

        except Exception as pinecone_err:
            print(f"⚠️ AutoQA Pinecone data unavailable for call {call_id}: {pinecone_err}")
            # Continue without AutoQA Pinecone data

        db_connections.close_connections()

        # Convert ObjectIds to strings
        qa_doc_clean = convert_objectid_to_string(qa_doc) if qa_doc else None
        transcript_doc_clean = convert_objectid_to_string(transcript_doc) if transcript_doc else None

        return jsonify({
            'success': True,
            'call_id': call_id,
            'qa_analysis': qa_doc_clean,
            'transcript': transcript_doc_clean,
            'autoqa_transcript_data': autoqa_transcript_data,
            'autoqa_qa_data': autoqa_qa_data,
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e),
            'call_id': call_id,
            'timestamp': datetime.now().isoformat()
        }), 500

@app.route('/api/stats')
def get_stats():
    """Get overall statistics"""
    
    try:
        db_connections = DatabaseConnections()
        
        # MongoDB stats
        qa_collection = db_connections.get_mongodb_collection('qa_analysis')
        transcript_collection = db_connections.get_mongodb_collection('call_smart_speech_transcribe')
        
        qa_count = qa_collection.count_documents({})
        transcript_count = transcript_collection.count_documents({})
        
        # Try to get Pinecone stats (gracefully handle auth errors)
        pinecone_data = {'status': 'unavailable', 'error': None}
        try:
            index = db_connections.get_pinecone_index()
            pinecone_stats = index.describe_index_stats()
            pinecone_data = {
                'status': 'available',
                'total_vectors': pinecone_stats.total_vector_count,
                'dimension': pinecone_stats.dimension,
                'index_fullness': pinecone_stats.index_fullness
            }
        except Exception as pinecone_err:
            pinecone_data['error'] = str(pinecone_err)
            print(f"⚠️ Pinecone stats unavailable: {pinecone_err}")
        
        # Score distribution
        score_distribution = {}
        for doc in qa_collection.find({}, {'call_quality_score': 1}):
            score = doc.get('call_quality_score')
            if score:
                score_key = f"Score {score}"
                score_distribution[score_key] = score_distribution.get(score_key, 0) + 1
        
        db_connections.close_connections()
        
        return jsonify({
            'success': True,
            'mongodb': {
                'qa_analyses': qa_count,
                'transcripts': transcript_count
            },
            'pinecone': pinecone_data,
            'score_distribution': score_distribution,
            'timestamp': datetime.now().isoformat()
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

@app.route('/api/autoqa/search/<call_id>')
def search_autoqa_by_call_id(call_id):
    """Search AutoQA data by call_id across both Pinecone indexes"""

    try:
        db_connections = DatabaseConnections()

        # Search in autoqa-transcripts index
        transcript_data = None
        qa_data = None

        try:
            # Get transcript data
            transcripts_index = db_connections.get_pinecone_index('autoqa-transcripts')
            transcript_query = transcripts_index.query(
                vector=[0.0] * 1536,
                filter={'call_id': call_id},
                top_k=1,
                include_metadata=True
            )

            if transcript_query.matches:
                transcript_metadata = transcript_query.matches[0].metadata
                transcript_data = {
                    'call_id': transcript_metadata.get('call_id'),
                    'folder_id': transcript_metadata.get('folder_id'),
                    'transcript_length': transcript_metadata.get('transcript_length'),
                    'conversation_segments': transcript_metadata.get('conversation_segments'),
                    'qa_status': transcript_metadata.get('qa_status'),
                    'qa_score': transcript_metadata.get('qa_score'),
                    'migrated_at': transcript_metadata.get('migrated_at'),
                    'tran_text': transcript_metadata.get('tran_text'),
                    'agent_diarize_transcript': transcript_metadata.get('agent_diarize_transcript'),
                    'client_diarize_transcript': transcript_metadata.get('client_diarize_transcript'),
                    'speaker_diarize_transcript': transcript_metadata.get('speaker_diarize_transcript')
                }

            # Get QA results data
            qa_results_index = db_connections.get_pinecone_index('autoqa-qa-results')
            qa_query = qa_results_index.query(
                vector=[0.0] * 1536,
                filter={'call_id': call_id},
                top_k=1,
                include_metadata=True
            )

            if qa_query.matches:
                qa_metadata = qa_query.matches[0].metadata
                qa_data = {
                    'call_id': qa_metadata.get('call_id'),
                    'csat': qa_metadata.get('csat'),
                    'call_verdict': qa_metadata.get('call_verdict'),
                    'issue_resolved': qa_metadata.get('issue_resolved'),
                    'call_topic': qa_metadata.get('call_topic'),
                    'overall_score_percentage': qa_metadata.get('overall_score_percentage'),
                    'analyzed_at': qa_metadata.get('analyzed_at'),
                    'analysis_method': qa_metadata.get('analysis_method'),
                    'qa_status': qa_metadata.get('qa_status')
                }

                # Parse JSON fields
                if qa_metadata.get('full_qa_result_json'):
                    try:
                        qa_data['full_qa_result'] = json.loads(qa_metadata['full_qa_result_json'])
                    except json.JSONDecodeError:
                        qa_data['full_qa_result'] = None

                if qa_metadata.get('assessment_table_json'):
                    try:
                        qa_data['assessment_table'] = json.loads(qa_metadata['assessment_table_json'])
                    except json.JSONDecodeError:
                        qa_data['assessment_table'] = None

                if qa_metadata.get('agent_feedback_json'):
                    try:
                        qa_data['agent_feedback'] = json.loads(qa_metadata['agent_feedback_json'])
                    except json.JSONDecodeError:
                        qa_data['agent_feedback'] = None

        except Exception as search_err:
            print(f"⚠️ AutoQA search error for {call_id}: {search_err}")

        db_connections.close_connections()

        return jsonify({
            'success': True,
            'call_id': call_id,
            'found': bool(transcript_data or qa_data),
            'transcript_data': transcript_data,
            'qa_data': qa_data,
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e),
            'call_id': call_id,
            'timestamp': datetime.now().isoformat()
        }), 500

# New endpoint for processing records without duplicates
@app.route('/api/process-records', methods=['POST'])
def process_records():
    """Process unanalyzed records without duplicates"""
    try:
        # Get the limit from the request
        data = request.json
        limit = data.get('limit', 5)  # Default to 5 records
        
        print(f"🔄 Processing {limit} records without duplicates")
        
        # Initialize the processor with 1 worker (sequential processing for UI demo)
        processor = AutoQAParallelProcessor(max_workers=1)
        
        # Get unprocessed vectors
        unprocessed_vectors = get_unprocessed_vectors(processor, limit)
        
        if not unprocessed_vectors:
            return jsonify({
                'success': True,
                'message': 'No unprocessed records found',
                'summary': {
                    'total': 0,
                    'successful': 0,
                    'failed': 0
                },
                'results': []
            })
        
        # Process each vector one by one
        results = []
        successful = 0
        failed = 0
        
        for vector in unprocessed_vectors:
            result = processor.process_single_call(vector)
            results.append(result)
            
            if result['status'] == 'success':
                successful += 1
                print(f"✅ Processed {result['call_id']} successfully")
            else:
                failed += 1
                print(f"❌ Failed to process {result['call_id']}: {result.get('error', 'Unknown error')}")
        
        # Return the results
        return jsonify({
            'success': True,
            'summary': {
                'total': len(unprocessed_vectors),
                'successful': successful,
                'failed': failed
            },
            'results': results,
            'timestamp': datetime.now().isoformat()
        })
        
    except Exception as e:
        print(f"❌ Error processing records: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

def get_unprocessed_vectors(processor, limit):
    """Get unprocessed vectors from Pinecone that haven't been processed yet"""
    try:
        # Get all vectors from Pinecone
        all_vectors = processor.get_vectors_from_pinecone(limit=100)  # Get more than we need to filter
        
        # Filter out vectors that have already been processed
        unprocessed_vectors = []
        
        # Connect to MySQL to check for existing records
        db_connections = DatabaseConnections()
        mysql_connection = db_connections.connect_mysql()
        
        for vector in all_vectors:
            call_id = vector.get('call_id')
            
            # Check if already processed in Pinecone metadata
            already_processed_pinecone = vector.get('metadata', {}).get('qa_analyzed', False)
            
            # Check if already processed in MySQL
            already_processed_mysql = is_processed_in_mysql(mysql_connection, call_id)
            
            # Only add if not processed in either system
            if not already_processed_pinecone and not already_processed_mysql:
                unprocessed_vectors.append(vector)
                
                # Stop once we have enough
                if len(unprocessed_vectors) >= limit:
                    break
        
        # Close MySQL connection
        mysql_connection.close()
        
        print(f"Found {len(unprocessed_vectors)} unprocessed vectors out of {len(all_vectors)} total")
        return unprocessed_vectors
        
    except Exception as e:
        print(f"❌ Error getting unprocessed vectors: {e}")
        return []

def is_processed_in_mysql(mysql_connection, call_id):
    """Check if a call_id has already been processed in MySQL"""
    try:
        cursor = mysql_connection.cursor()
        cursor.execute("SELECT call_id FROM qa_results WHERE call_id = %s", (call_id,))
        result = cursor.fetchone()
        cursor.close()
        
        return result is not None
        
    except Exception as e:
        print(f"❌ Error checking MySQL for {call_id}: {e}")
        return False

@app.route('/health')
def health_check():
    """Simple health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.now().isoformat()
    })

if __name__ == '__main__':
    print("🚀 Starting AutoQA Results Server")
    print("=" * 40)
    print("📊 Available endpoints:")
    print("  http://localhost:5000/           - Main results viewer")
    print("  http://localhost:5000/api/qa-results - QA results API")
    print("  http://localhost:5000/api/stats      - Statistics API")
    print("  http://localhost:5000/api/process-records - Process records API (POST)")
    print("  http://localhost:5000/health        - Health check")
    print("=" * 40)
    
    # Run the Flask app
    app.run(
        host='0.0.0.0',
        port=5000,
        debug=True,
        use_reloader=False  # Disable reloader to avoid issues with imports
    )
