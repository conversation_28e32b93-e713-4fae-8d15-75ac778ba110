#!/usr/bin/env python3
"""
Comprehensive AutoQA System Audit
Identifies and fixes synchronization issues between MongoDB, Pinecone, and MySQL
"""

import sys
import os
import json
from pathlib import Path
from typing import Dict, List, Any, Set
from datetime import datetime

# Add src to path and load environment
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root / "src"))

from dotenv import load_dotenv
load_dotenv(project_root / ".env", override=True)

from database.connections import DatabaseConnections

class SystemAuditor:
    """Comprehensive system auditor for AutoQA"""
    
    def __init__(self):
        self.db_connections = DatabaseConnections()
        self.audit_results = {}
        
    def audit_mongodb_data(self) -> Dict[str, Any]:
        """Audit MongoDB data sources"""
        print("🔍 AUDITING MONGODB DATA")
        print("=" * 50)
        
        try:
            mongodb_client = self.db_connections.connect_mongodb()
            db_name = os.getenv('MONGODB_DATABASE', 'your_database')
            mongodb = mongodb_client[db_name]
            
            # Check transcript collection
            transcript_collection = mongodb['call_smart_speech_transcribe']
            transcript_count = transcript_collection.count_documents({})
            
            # Check QA analysis collection
            qa_collection = mongodb['qa_analysis']
            qa_count = qa_collection.count_documents({})
            
            # Sample transcript data
            sample_transcripts = list(transcript_collection.find().limit(5))
            transcript_call_ids = [doc.get('call_id') for doc in sample_transcripts if doc.get('call_id')]
            
            # Sample QA data
            sample_qa = list(qa_collection.find().limit(5))
            qa_call_ids = [doc.get('call_id') for doc in sample_qa if doc.get('call_id')]
            
            mongodb_audit = {
                'transcript_count': transcript_count,
                'qa_analysis_count': qa_count,
                'sample_transcript_call_ids': transcript_call_ids[:3],
                'sample_qa_call_ids': qa_call_ids[:3],
                'collections': mongodb.list_collection_names()
            }
            
            print(f"✅ MongoDB Transcript Records: {transcript_count}")
            print(f"✅ MongoDB QA Analysis Records: {qa_count}")
            print(f"📋 Sample Transcript Call IDs: {transcript_call_ids[:3]}")
            print(f"📋 Sample QA Call IDs: {qa_call_ids[:3]}")
            
            return mongodb_audit
            
        except Exception as e:
            print(f"❌ MongoDB Audit Error: {e}")
            return {'error': str(e)}
    
    def audit_pinecone_data(self) -> Dict[str, Any]:
        """Audit Pinecone vector data"""
        print("\n🔍 AUDITING PINECONE DATA")
        print("=" * 50)
        
        try:
            # Audit transcripts index
            transcripts_index = self.db_connections.get_pinecone_index('autoqa-transcripts')
            
            # Get all vectors
            transcript_query = transcripts_index.query(
                vector=[0.0] * 1536,
                top_k=10000,
                include_metadata=True,
                filter={'autoqa_managed': True}
            )
            
            total_transcripts = len(transcript_query.matches)
            processed_transcripts = 0
            unprocessed_transcripts = 0
            transcript_call_ids = []
            
            for match in transcript_query.matches:
                metadata = match.metadata
                call_id = metadata.get('call_id')
                qa_status = metadata.get('qa_status', 'unprocessed')
                
                transcript_call_ids.append(call_id)
                
                if qa_status == 'processed':
                    processed_transcripts += 1
                else:
                    unprocessed_transcripts += 1
            
            # Audit QA results index
            qa_results_index = self.db_connections.get_pinecone_index('autoqa-qa-results')
            
            qa_query = qa_results_index.query(
                vector=[0.0] * 1536,
                top_k=10000,
                include_metadata=True,
                filter={'autoqa_managed': True}
            )
            
            total_qa_results = len(qa_query.matches)
            qa_call_ids = []
            
            for match in qa_query.matches:
                metadata = match.metadata
                call_id = metadata.get('call_id')
                qa_call_ids.append(call_id)
            
            pinecone_audit = {
                'transcripts_total': total_transcripts,
                'transcripts_processed': processed_transcripts,
                'transcripts_unprocessed': unprocessed_transcripts,
                'qa_results_total': total_qa_results,
                'transcript_call_ids': transcript_call_ids[:5],
                'qa_call_ids': qa_call_ids[:5]
            }
            
            print(f"✅ Pinecone Transcript Vectors: {total_transcripts}")
            print(f"   - Processed: {processed_transcripts}")
            print(f"   - Unprocessed: {unprocessed_transcripts}")
            print(f"✅ Pinecone QA Result Vectors: {total_qa_results}")
            print(f"📋 Sample Transcript Call IDs: {transcript_call_ids[:3]}")
            print(f"📋 Sample QA Result Call IDs: {qa_call_ids[:3]}")
            
            return pinecone_audit
            
        except Exception as e:
            print(f"❌ Pinecone Audit Error: {e}")
            return {'error': str(e)}
    
    def audit_mysql_data(self) -> Dict[str, Any]:
        """Audit MySQL data"""
        print("\n🔍 AUDITING MYSQL DATA")
        print("=" * 50)
        
        try:
            mysql_conn = self.db_connections.connect_mysql()
            cursor = mysql_conn.cursor(dictionary=True)
            
            # Count total records
            cursor.execute("SELECT COUNT(*) as count FROM qa_results")
            total_count = cursor.fetchone()['count']
            
            # Get sample records
            cursor.execute("""
                SELECT call_id, overall_score_percentage, csat, call_verdict, 
                       analyzed_at, analysis_method, status
                FROM qa_results 
                ORDER BY processed_at DESC 
                LIMIT 10
            """)
            
            sample_records = cursor.fetchall()
            mysql_call_ids = [record['call_id'] for record in sample_records]
            
            # Check for duplicates
            cursor.execute("""
                SELECT call_id, COUNT(*) as count 
                FROM qa_results 
                GROUP BY call_id 
                HAVING COUNT(*) > 1
            """)
            
            duplicates = cursor.fetchall()
            
            cursor.close()
            
            mysql_audit = {
                'total_records': total_count,
                'sample_call_ids': mysql_call_ids[:5],
                'duplicates': len(duplicates),
                'duplicate_call_ids': [dup['call_id'] for dup in duplicates]
            }
            
            print(f"✅ MySQL QA Results: {total_count}")
            print(f"📋 Sample Call IDs: {mysql_call_ids[:3]}")
            print(f"⚠️ Duplicate Records: {len(duplicates)}")
            
            if duplicates:
                print(f"🔄 Duplicate Call IDs: {[dup['call_id'] for dup in duplicates]}")
            
            return mysql_audit
            
        except Exception as e:
            print(f"❌ MySQL Audit Error: {e}")
            return {'error': str(e)}
    
    def analyze_synchronization(self, mongodb_audit: Dict, pinecone_audit: Dict, mysql_audit: Dict) -> Dict[str, Any]:
        """Analyze synchronization between systems"""
        print("\n🔍 ANALYZING SYNCHRONIZATION")
        print("=" * 50)
        
        # Get call ID sets
        mongodb_transcript_ids = set(mongodb_audit.get('sample_transcript_call_ids', []))
        mongodb_qa_ids = set(mongodb_audit.get('sample_qa_call_ids', []))
        pinecone_transcript_ids = set(pinecone_audit.get('transcript_call_ids', []))
        pinecone_qa_ids = set(pinecone_audit.get('qa_call_ids', []))
        mysql_ids = set(mysql_audit.get('sample_call_ids', []))
        
        # Find overlaps and gaps
        sync_analysis = {
            'mongodb_to_pinecone_transcripts': len(mongodb_transcript_ids.intersection(pinecone_transcript_ids)),
            'pinecone_to_mysql': len(pinecone_qa_ids.intersection(mysql_ids)),
            'mongodb_qa_to_mysql': len(mongodb_qa_ids.intersection(mysql_ids)),
            
            'only_in_mongodb': list(mongodb_transcript_ids - pinecone_transcript_ids),
            'only_in_pinecone_transcripts': list(pinecone_transcript_ids - mysql_ids),
            'only_in_mysql': list(mysql_ids - pinecone_qa_ids),
            
            'total_unique_call_ids': len(mongodb_transcript_ids.union(pinecone_transcript_ids).union(mysql_ids))
        }
        
        print(f"📊 Synchronization Analysis:")
        print(f"   MongoDB → Pinecone Transcripts: {sync_analysis['mongodb_to_pinecone_transcripts']} matches")
        print(f"   Pinecone QA → MySQL: {sync_analysis['pinecone_to_mysql']} matches")
        print(f"   MongoDB QA → MySQL: {sync_analysis['mongodb_qa_to_mysql']} matches")
        print(f"   Total Unique Call IDs: {sync_analysis['total_unique_call_ids']}")
        
        if sync_analysis['only_in_mongodb']:
            print(f"⚠️ Only in MongoDB: {sync_analysis['only_in_mongodb']}")
        if sync_analysis['only_in_pinecone_transcripts']:
            print(f"⚠️ Only in Pinecone: {sync_analysis['only_in_pinecone_transcripts']}")
        if sync_analysis['only_in_mysql']:
            print(f"⚠️ Only in MySQL: {sync_analysis['only_in_mysql']}")
        
        return sync_analysis
    
    def identify_web_interface_source(self) -> Dict[str, Any]:
        """Identify what data source the web interface is using"""
        print("\n🔍 IDENTIFYING WEB INTERFACE DATA SOURCE")
        print("=" * 50)
        
        try:
            import requests
            
            response = requests.get('http://localhost:5000/api/qa-results', timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                
                web_analysis = {
                    'api_success': data.get('success'),
                    'total_count': data.get('summary', {}).get('total_count'),
                    'mysql_results': data.get('summary', {}).get('mysql_results'),
                    'mongodb_results': data.get('summary', {}).get('mongodb_results'),
                    'data_sources_used': []
                }
                
                if data.get('qa_results'):
                    # Check data sources
                    for result in data['qa_results']:
                        source = result.get('data_source')
                        if source and source not in web_analysis['data_sources_used']:
                            web_analysis['data_sources_used'].append(source)
                
                print(f"✅ Web API Response: Success")
                print(f"📊 Total Records Displayed: {web_analysis['total_count']}")
                print(f"📊 MySQL Results: {web_analysis['mysql_results']}")
                print(f"📊 MongoDB Results: {web_analysis['mongodb_results']}")
                print(f"📋 Data Sources Used: {web_analysis['data_sources_used']}")
                
                return web_analysis
            else:
                print(f"❌ Web API Error: {response.status_code}")
                return {'error': f'API returned {response.status_code}'}
                
        except Exception as e:
            print(f"❌ Web Interface Check Error: {e}")
            return {'error': str(e)}
    
    def run_complete_audit(self) -> Dict[str, Any]:
        """Run complete system audit"""
        print("🔍 AUTOQA SYSTEM COMPREHENSIVE AUDIT")
        print("=" * 60)
        
        # Audit each component
        mongodb_audit = self.audit_mongodb_data()
        pinecone_audit = self.audit_pinecone_data()
        mysql_audit = self.audit_mysql_data()
        
        # Analyze synchronization
        sync_analysis = self.analyze_synchronization(mongodb_audit, pinecone_audit, mysql_audit)
        
        # Check web interface
        web_analysis = self.identify_web_interface_source()
        
        # Generate recommendations
        recommendations = self.generate_recommendations(mongodb_audit, pinecone_audit, mysql_audit, sync_analysis, web_analysis)
        
        audit_results = {
            'mongodb': mongodb_audit,
            'pinecone': pinecone_audit,
            'mysql': mysql_audit,
            'synchronization': sync_analysis,
            'web_interface': web_analysis,
            'recommendations': recommendations,
            'audit_timestamp': datetime.now().isoformat()
        }
        
        self.audit_results = audit_results
        return audit_results
    
    def generate_recommendations(self, mongodb_audit, pinecone_audit, mysql_audit, sync_analysis, web_analysis) -> List[str]:
        """Generate specific recommendations to fix issues"""
        recommendations = []
        
        # Check for data count mismatches
        web_count = web_analysis.get('total_count', 0)
        mysql_count = mysql_audit.get('total_records', 0)
        pinecone_qa_count = pinecone_audit.get('qa_results_total', 0)
        
        if web_count != mysql_count:
            recommendations.append(f"🔧 CRITICAL: Web interface shows {web_count} records but MySQL has {mysql_count}. Fix data source priority in web server.")
        
        if mysql_count < pinecone_qa_count:
            recommendations.append(f"🔧 SYNC ISSUE: Pinecone has {pinecone_qa_count} QA results but MySQL only has {mysql_count}. Run MySQL sync.")
        
        if pinecone_audit.get('transcripts_unprocessed', 0) == 0:
            recommendations.append("🔧 PROCESSING ISSUE: No unprocessed records available. Reset some processed records for demo.")
        
        if mysql_audit.get('duplicates', 0) > 0:
            recommendations.append("🔧 DATA INTEGRITY: Duplicate records found in MySQL. Clean up duplicates.")
        
        if 'error' in web_analysis:
            recommendations.append("🔧 WEB INTERFACE: Web server not responding. Restart web server.")
        
        return recommendations
    
    def close_connections(self):
        """Close all database connections"""
        self.db_connections.close_connections()

def main():
    """Main audit function"""
    auditor = SystemAuditor()
    
    try:
        audit_results = auditor.run_complete_audit()
        
        print("\n" + "=" * 60)
        print("🎯 AUDIT SUMMARY & RECOMMENDATIONS")
        print("=" * 60)
        
        recommendations = audit_results['recommendations']
        
        if recommendations:
            print("\n🔧 CRITICAL ISSUES TO FIX:")
            for i, rec in enumerate(recommendations, 1):
                print(f"{i}. {rec}")
        else:
            print("\n✅ NO CRITICAL ISSUES FOUND")
        
        print(f"\n📊 SYSTEM STATUS:")
        print(f"   MongoDB Transcripts: {audit_results['mongodb'].get('transcript_count', 'Error')}")
        print(f"   Pinecone Transcripts: {audit_results['pinecone'].get('transcripts_total', 'Error')}")
        print(f"   Pinecone QA Results: {audit_results['pinecone'].get('qa_results_total', 'Error')}")
        print(f"   MySQL QA Results: {audit_results['mysql'].get('total_records', 'Error')}")
        print(f"   Web Interface Display: {audit_results['web_interface'].get('total_count', 'Error')}")
        
        return 0 if not recommendations else 1
        
    except Exception as e:
        print(f"❌ Audit failed: {e}")
        return 1
    
    finally:
        auditor.close_connections()

if __name__ == "__main__":
    sys.exit(main())
