#!/usr/bin/env python3
"""
AutoQA Deployment Automation Script
Automates the deployment process for AutoQA project migration
"""

import sys
import os
import subprocess
import shutil
import time
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Tuple, Optional

def print_header(title: str):
    """Print formatted header"""
    print("\n" + "=" * 60)
    print(f"🚀 {title}")
    print("=" * 60)

def print_step(step: str, description: str):
    """Print formatted step"""
    print(f"\n📋 {step}: {description}")
    print("-" * 40)

def run_command(command: str, description: str, check: bool = True) -> Tuple[bool, str]:
    """Run a command and return success status and output"""
    print(f"⚡ {description}...")
    print(f"   Command: {command}")
    
    try:
        if sys.platform == "win32":
            # Windows
            result = subprocess.run(command, shell=True, capture_output=True, text=True, check=check)
        else:
            # Linux/Mac
            result = subprocess.run(command, shell=True, capture_output=True, text=True, check=check)
        
        if result.returncode == 0:
            print(f"✅ {description} completed successfully")
            return True, result.stdout
        else:
            print(f"❌ {description} failed")
            print(f"   Error: {result.stderr}")
            return False, result.stderr
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed with error: {e}")
        return False, str(e)
    except Exception as e:
        print(f"❌ Unexpected error in {description}: {e}")
        return False, str(e)

def check_prerequisites() -> bool:
    """Check system prerequisites"""
    print_step("STEP 1", "Checking Prerequisites")
    
    all_good = True
    
    # Check Python version
    version = sys.version_info
    if version.major >= 3 and version.minor >= 9:
        print(f"✅ Python {version.major}.{version.minor}.{version.micro} (compatible)")
    else:
        print(f"❌ Python {version.major}.{version.minor}.{version.micro} (requires 3.9+)")
        all_good = False
    
    # Check pip
    success, _ = run_command("pip --version", "Checking pip", check=False)
    if not success:
        print("❌ pip not available")
        all_good = False
    
    # Check git (optional)
    success, _ = run_command("git --version", "Checking git", check=False)
    if success:
        print("✅ Git available")
    else:
        print("⚠️ Git not available (optional)")
    
    # Check project structure
    project_root = Path.cwd()
    required_dirs = ['src', 'scripts', 'web', 'test', 'docs']
    required_files = ['requirements.txt', '.env.example', 'README.md']
    
    print("\n📁 Checking project structure...")
    for dir_name in required_dirs:
        if (project_root / dir_name).exists():
            print(f"✅ {dir_name}/ directory found")
        else:
            print(f"❌ {dir_name}/ directory missing")
            all_good = False
    
    for file_name in required_files:
        if (project_root / file_name).exists():
            print(f"✅ {file_name} found")
        else:
            print(f"❌ {file_name} missing")
            all_good = False
    
    return all_good

def setup_virtual_environment() -> bool:
    """Set up Python virtual environment"""
    print_step("STEP 2", "Setting Up Virtual Environment")
    
    venv_path = Path("myenv")
    
    # Remove existing virtual environment if it exists
    if venv_path.exists():
        print("🗑️ Removing existing virtual environment...")
        try:
            shutil.rmtree(venv_path)
            print("✅ Existing virtual environment removed")
        except Exception as e:
            print(f"❌ Failed to remove existing virtual environment: {e}")
            return False
    
    # Create new virtual environment
    success, output = run_command(f"{sys.executable} -m venv myenv", "Creating virtual environment")
    if not success:
        return False
    
    # Determine activation script path
    if sys.platform == "win32":
        activate_script = venv_path / "Scripts" / "activate.bat"
        python_exe = venv_path / "Scripts" / "python.exe"
        pip_exe = venv_path / "Scripts" / "pip.exe"
    else:
        activate_script = venv_path / "bin" / "activate"
        python_exe = venv_path / "bin" / "python"
        pip_exe = venv_path / "bin" / "pip"
    
    # Verify virtual environment was created
    if python_exe.exists():
        print("✅ Virtual environment created successfully")
        print(f"   Python executable: {python_exe}")
        print(f"   Activation script: {activate_script}")
        return True
    else:
        print("❌ Virtual environment creation failed")
        return False

def install_dependencies() -> bool:
    """Install project dependencies"""
    print_step("STEP 3", "Installing Dependencies")
    
    # Determine pip path
    if sys.platform == "win32":
        pip_exe = Path("myenv") / "Scripts" / "pip.exe"
    else:
        pip_exe = Path("myenv") / "bin" / "pip"
    
    if not pip_exe.exists():
        print("❌ Virtual environment pip not found")
        return False
    
    # Upgrade pip
    success, _ = run_command(f"{pip_exe} install --upgrade pip", "Upgrading pip")
    if not success:
        return False
    
    # Install requirements
    success, _ = run_command(f"{pip_exe} install -r requirements.txt", "Installing requirements")
    if not success:
        print("⚠️ Some packages may have failed to install. Trying individual installation...")
        
        # Try installing critical packages individually
        critical_packages = [
            "python-dotenv==1.0.0",
            "pymongo==4.6.1", 
            "pinecone-client==3.0.0",
            "openai==1.12.0",
            "mysql-connector-python==8.3.0",
            "Flask==3.0.0",
            "Flask-CORS==4.0.0"
        ]
        
        for package in critical_packages:
            success, _ = run_command(f"{pip_exe} install {package}", f"Installing {package}", check=False)
            if not success:
                print(f"⚠️ Failed to install {package}")
    
    # Verify critical imports
    python_exe = Path("myenv") / ("Scripts" if sys.platform == "win32" else "bin") / "python"
    
    test_imports = [
        "import pymongo; print('✅ pymongo')",
        "import pinecone; print('✅ pinecone')", 
        "import openai; print('✅ openai')",
        "import mysql.connector; print('✅ mysql.connector')",
        "import flask; print('✅ flask')"
    ]
    
    print("\n🔍 Verifying critical imports...")
    all_imports_ok = True
    
    for test_import in test_imports:
        success, output = run_command(f'{python_exe} -c "{test_import}"', f"Testing import", check=False)
        if not success:
            all_imports_ok = False
    
    return all_imports_ok

def setup_environment_file() -> bool:
    """Set up environment configuration file"""
    print_step("STEP 4", "Setting Up Environment Configuration")
    
    env_file = Path(".env")
    env_example = Path(".env.example")
    
    if env_file.exists():
        print("✅ .env file already exists")
        
        # Ask user if they want to update it
        response = input("🤔 .env file exists. Do you want to recreate it from template? (y/N): ").strip().lower()
        if response != 'y':
            print("⏭️ Keeping existing .env file")
            return True
    
    if not env_example.exists():
        print("❌ .env.example template not found")
        return False
    
    # Copy template to .env
    try:
        shutil.copy2(env_example, env_file)
        print("✅ .env file created from template")
        
        print("\n⚠️ IMPORTANT: You must edit .env file with your actual values:")
        print("   - MONGODB_CONNECTION_STRING")
        print("   - PINECONE_API_KEY") 
        print("   - OPENAI_API_KEY")
        print("   - MYSQL_PASSWORD")
        print("\n💡 Edit .env file now before continuing")
        
        return True
    except Exception as e:
        print(f"❌ Failed to create .env file: {e}")
        return False

def validate_installation() -> bool:
    """Validate the installation"""
    print_step("STEP 5", "Validating Installation")
    
    # Run validation script
    python_exe = Path("myenv") / ("Scripts" if sys.platform == "win32" else "bin") / "python"
    
    success, output = run_command(f"{python_exe} scripts/validate_requirements.py", "Running validation script")
    
    if success:
        print("✅ Installation validation passed")
        return True
    else:
        print("❌ Installation validation failed")
        print("💡 Check the validation output above for specific issues")
        return False

def run_tests() -> bool:
    """Run system tests"""
    print_step("STEP 6", "Running System Tests")
    
    python_exe = Path("myenv") / ("Scripts" if sys.platform == "win32" else "bin") / "python"
    
    print("⚠️ Note: Tests require properly configured .env file with valid credentials")
    response = input("🤔 Do you want to run system tests now? (y/N): ").strip().lower()
    
    if response != 'y':
        print("⏭️ Skipping system tests")
        return True
    
    success, output = run_command(f"{python_exe} test/test_complete_implementation.py", "Running complete implementation test")
    
    if success:
        print("✅ System tests passed")
        return True
    else:
        print("❌ System tests failed")
        print("💡 This may be due to missing database credentials or services not running")
        return False

def create_deployment_record():
    """Create deployment record"""
    print_step("FINAL", "Creating Deployment Record")
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    record_file = Path(f"deployment_record_{timestamp}.txt")
    
    try:
        with open(record_file, 'w') as f:
            f.write("AutoQA Deployment Record\n")
            f.write("========================\n\n")
            f.write(f"Deployment Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"Target Machine: {os.environ.get('COMPUTERNAME', os.environ.get('HOSTNAME', 'Unknown'))}\n")
            f.write(f"Python Version: {sys.version}\n")
            f.write(f"Virtual Environment: myenv\n")
            f.write(f"Project Location: {Path.cwd().absolute()}\n")
            f.write(f"Deployment Script: {__file__}\n\n")
            
            f.write("Deployment Steps Completed:\n")
            f.write("- ✅ Prerequisites checked\n")
            f.write("- ✅ Virtual environment created\n") 
            f.write("- ✅ Dependencies installed\n")
            f.write("- ✅ Environment file configured\n")
            f.write("- ✅ Installation validated\n\n")
            
            f.write("Next Steps:\n")
            f.write("1. Edit .env file with your actual credentials\n")
            f.write("2. Run: python test/test_complete_implementation.py\n")
            f.write("3. Start web console: python web/qa_results_server.py\n")
            f.write("4. Test parallel processing: python scripts/autoqa_parallel_processor.py --limit 3\n")
        
        print(f"✅ Deployment record created: {record_file}")
        
    except Exception as e:
        print(f"⚠️ Failed to create deployment record: {e}")

def main():
    """Main deployment function"""
    print_header("AutoQA Project Deployment Automation")
    
    print("🎯 This script will:")
    print("   1. Check system prerequisites")
    print("   2. Set up Python virtual environment (myenv)")
    print("   3. Install all required dependencies")
    print("   4. Configure environment file")
    print("   5. Validate installation")
    print("   6. Run system tests (optional)")
    
    response = input("\n🤔 Do you want to proceed with automated deployment? (y/N): ").strip().lower()
    if response != 'y':
        print("❌ Deployment cancelled by user")
        return False
    
    start_time = time.time()
    
    # Step 1: Check prerequisites
    if not check_prerequisites():
        print("\n❌ Prerequisites check failed. Please fix issues and try again.")
        return False
    
    # Step 2: Set up virtual environment
    if not setup_virtual_environment():
        print("\n❌ Virtual environment setup failed.")
        return False
    
    # Step 3: Install dependencies
    if not install_dependencies():
        print("\n❌ Dependencies installation failed.")
        return False
    
    # Step 4: Set up environment file
    if not setup_environment_file():
        print("\n❌ Environment file setup failed.")
        return False
    
    # Step 5: Validate installation
    if not validate_installation():
        print("\n⚠️ Installation validation failed, but continuing...")
    
    # Step 6: Run tests (optional)
    run_tests()
    
    # Create deployment record
    create_deployment_record()
    
    # Summary
    elapsed_time = time.time() - start_time
    print_header("Deployment Complete")
    
    print(f"⏱️ Total deployment time: {elapsed_time:.1f} seconds")
    print("\n🎉 AutoQA deployment automation completed!")
    
    print("\n📋 Next Steps:")
    print("1. 🔧 Edit .env file with your actual credentials:")
    print("   - MongoDB connection string")
    print("   - Pinecone API key")
    print("   - OpenAI API key") 
    print("   - MySQL password")
    
    print("\n2. 🧪 Test the installation:")
    if sys.platform == "win32":
        print("   myenv\\Scripts\\activate")
    else:
        print("   source myenv/bin/activate")
    print("   python test/test_complete_implementation.py")
    
    print("\n3. 🚀 Start using AutoQA:")
    print("   python web/qa_results_server.py")
    print("   python scripts/autoqa_parallel_processor.py --limit 3")
    
    print("\n✅ AutoQA is ready for use!")
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
