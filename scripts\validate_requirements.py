#!/usr/bin/env python3
"""
Requirements Validation Script for AutoQA Project
Validates that all dependencies are properly specified and compatible
"""

import sys
import os
import subprocess
import importlib
from pathlib import Path
from typing import Dict, List, Tuple, Optional

# Add src to path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root / "src"))

def check_python_version() -> bool:
    """Check if Python version meets requirements"""
    print("🐍 Checking Python Version...")
    
    version = sys.version_info
    required_major, required_minor = 3, 9
    
    if version.major >= required_major and version.minor >= required_minor:
        print(f"✅ Python {version.major}.{version.minor}.{version.micro} (meets requirement: {required_major}.{required_minor}+)")
        return True
    else:
        print(f"❌ Python {version.major}.{version.minor}.{version.micro} (requires: {required_major}.{required_minor}+)")
        return False

def read_requirements_file() -> List[str]:
    """Read and parse requirements.txt"""
    print("\n📋 Reading requirements.txt...")
    
    requirements_file = project_root / "requirements.txt"
    if not requirements_file.exists():
        print("❌ requirements.txt not found!")
        return []
    
    with open(requirements_file, 'r') as f:
        lines = f.readlines()
    
    # Parse requirements (ignore comments and empty lines)
    requirements = []
    for line in lines:
        line = line.strip()
        if line and not line.startswith('#'):
            requirements.append(line)
    
    print(f"✅ Found {len(requirements)} package requirements")
    return requirements

def check_package_installation(requirements: List[str]) -> Dict[str, bool]:
    """Check if all required packages are installed"""
    print("\n📦 Checking Package Installation...")
    
    results = {}
    
    for requirement in requirements:
        # Extract package name (before ==, >=, etc.)
        package_name = requirement.split('==')[0].split('>=')[0].split('<=')[0].split('~=')[0].strip()
        
        try:
            importlib.import_module(package_name.replace('-', '_'))
            print(f"✅ {package_name}: Installed")
            results[package_name] = True
        except ImportError:
            print(f"❌ {package_name}: Not installed")
            results[package_name] = False
    
    return results

def check_critical_imports() -> Dict[str, bool]:
    """Test critical AutoQA imports"""
    print("\n🔍 Testing Critical AutoQA Imports...")
    
    critical_imports = {
        'python-dotenv': 'dotenv',
        'pymongo': 'pymongo',
        'pinecone-client': 'pinecone',
        'openai': 'openai',
        'mysql-connector-python': 'mysql.connector',
        'Flask': 'flask',
        'Flask-CORS': 'flask_cors',
        'pandas': 'pandas',
        'numpy': 'numpy',
        'bson': 'bson'
    }
    
    results = {}
    
    for package_name, import_name in critical_imports.items():
        try:
            importlib.import_module(import_name)
            print(f"✅ {package_name} ({import_name}): Import successful")
            results[package_name] = True
        except ImportError as e:
            print(f"❌ {package_name} ({import_name}): Import failed - {e}")
            results[package_name] = False
    
    return results

def check_autoqa_modules() -> Dict[str, bool]:
    """Test AutoQA-specific module imports"""
    print("\n🔧 Testing AutoQA Module Imports...")
    
    autoqa_modules = {
        'database.connections': 'DatabaseConnections',
        'parallel_processing.manager': 'ParallelProcessingManager',
    }
    
    results = {}
    
    for module_name, class_name in autoqa_modules.items():
        try:
            module = importlib.import_module(module_name)
            if hasattr(module, class_name):
                print(f"✅ {module_name}.{class_name}: Available")
                results[module_name] = True
            else:
                print(f"⚠️ {module_name}: Module exists but {class_name} not found")
                results[module_name] = False
        except ImportError as e:
            print(f"❌ {module_name}: Import failed - {e}")
            results[module_name] = False
    
    return results

def check_version_compatibility() -> Dict[str, str]:
    """Check versions of critical packages"""
    print("\n📊 Checking Package Versions...")
    
    version_checks = {
        'python-dotenv': 'dotenv',
        'pymongo': 'pymongo',
        'pinecone-client': 'pinecone',
        'openai': 'openai',
        'Flask': 'flask',
        'pandas': 'pandas',
        'numpy': 'numpy'
    }
    
    versions = {}
    
    for package_name, import_name in version_checks.items():
        try:
            module = importlib.import_module(import_name)
            version = getattr(module, '__version__', 'Unknown')
            print(f"✅ {package_name}: {version}")
            versions[package_name] = version
        except ImportError:
            print(f"❌ {package_name}: Not available")
            versions[package_name] = 'Not installed'
    
    return versions

def check_environment_setup() -> bool:
    """Check if virtual environment is properly set up"""
    print("\n🌍 Checking Environment Setup...")
    
    # Check if in virtual environment
    in_venv = hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix)
    
    if in_venv:
        print(f"✅ Virtual environment active: {sys.prefix}")
    else:
        print("⚠️ Not in virtual environment (recommended to use virtual environment)")
    
    # Check Python executable location
    python_path = sys.executable
    print(f"📍 Python executable: {python_path}")
    
    # Check if myenv directory exists
    myenv_path = project_root / "myenv"
    if myenv_path.exists():
        print(f"✅ myenv directory found: {myenv_path}")
    else:
        print("⚠️ myenv directory not found")
    
    return in_venv

def generate_missing_packages_install_command(missing_packages: List[str]) -> str:
    """Generate pip install command for missing packages"""
    if not missing_packages:
        return ""
    
    return f"pip install {' '.join(missing_packages)}"

def main():
    """Main validation function"""
    print("🚀 AutoQA Requirements Validation")
    print("=" * 50)
    
    # Track overall success
    all_checks_passed = True
    
    # 1. Check Python version
    python_ok = check_python_version()
    if not python_ok:
        all_checks_passed = False
    
    # 2. Check environment setup
    env_ok = check_environment_setup()
    
    # 3. Read requirements
    requirements = read_requirements_file()
    if not requirements:
        all_checks_passed = False
        return
    
    # 4. Check package installation
    package_results = check_package_installation(requirements)
    missing_packages = [pkg for pkg, installed in package_results.items() if not installed]
    
    if missing_packages:
        all_checks_passed = False
        print(f"\n❌ Missing packages: {', '.join(missing_packages)}")
        install_cmd = generate_missing_packages_install_command(missing_packages)
        print(f"💡 Install command: {install_cmd}")
    
    # 5. Check critical imports
    import_results = check_critical_imports()
    failed_imports = [pkg for pkg, success in import_results.items() if not success]
    
    if failed_imports:
        all_checks_passed = False
        print(f"\n❌ Failed imports: {', '.join(failed_imports)}")
    
    # 6. Check AutoQA modules
    module_results = check_autoqa_modules()
    failed_modules = [mod for mod, success in module_results.items() if not success]
    
    if failed_modules:
        all_checks_passed = False
        print(f"\n❌ Failed AutoQA modules: {', '.join(failed_modules)}")
    
    # 7. Check versions
    versions = check_version_compatibility()
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 VALIDATION SUMMARY")
    print("=" * 50)
    
    print(f"Python Version: {'✅ PASS' if python_ok else '❌ FAIL'}")
    print(f"Virtual Environment: {'✅ ACTIVE' if env_ok else '⚠️ NOT ACTIVE'}")
    print(f"Package Installation: {'✅ COMPLETE' if not missing_packages else f'❌ MISSING {len(missing_packages)} PACKAGES'}")
    print(f"Critical Imports: {'✅ ALL WORKING' if not failed_imports else f'❌ {len(failed_imports)} FAILED'}")
    print(f"AutoQA Modules: {'✅ ALL WORKING' if not failed_modules else f'❌ {len(failed_modules)} FAILED'}")
    
    if all_checks_passed:
        print("\n🎉 ALL VALIDATION CHECKS PASSED!")
        print("✅ AutoQA project is ready for deployment")
        print("\nNext steps:")
        print("1. Run: python test/test_complete_implementation.py")
        print("2. Start web console: python web/qa_results_server.py")
    else:
        print("\n❌ VALIDATION FAILED!")
        print("⚠️ Please fix the issues above before deployment")
        
        if missing_packages:
            print(f"\n💡 Quick fix for missing packages:")
            print(f"   {generate_missing_packages_install_command(missing_packages)}")
        
        if not python_ok:
            print("\n💡 Python version fix:")
            print("   Install Python 3.9+ from python.org")
        
        if not env_ok:
            print("\n💡 Virtual environment fix:")
            print("   python -m venv myenv")
            print("   myenv\\Scripts\\activate  # Windows")
            print("   source myenv/bin/activate  # Linux/Mac")
    
    return all_checks_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
