# 🔧 AutoQA Migration Troubleshooting Guide

## 📋 Overview
This guide provides solutions to common issues encountered during AutoQA project migration to a new machine.

---

## 🐍 Python Environment Issues

### Issue 1: Python Version Compatibility
**Error**: `Python version not supported` or `SyntaxError: invalid syntax`

**Symptoms**:
- Installation fails with version errors
- Import errors with f-strings or type hints
- Package compatibility issues

**Solution**:
```bash
# Check current Python version
python --version

# If version < 3.9, install Python 3.9+
# Windows: Download from python.org
# Linux: 
sudo apt update
sudo apt install python3.9 python3.9-venv python3.9-pip

# Mac:
brew install python@3.9

# Verify installation
python3.9 --version
```

**Alternative Solution**:
```bash
# Use specific Python version for virtual environment
python3.9 -m venv myenv
# or
py -3.9 -m venv myenv  # Windows with Python Launcher
```

### Issue 2: Virtual Environment Not Activating
**Error**: `'myenv' is not recognized` or virtual environment commands not working

**Symptoms**:
- Virtual environment doesn't activate
- `which python` shows system Python instead of virtual environment
- Packages installed globally instead of in virtual environment

**Solution**:
```bash
# Delete corrupted virtual environment
rm -rf myenv  # Linux/Mac
rmdir /s myenv  # Windows

# Recreate virtual environment
python -m venv myenv

# Activate with full path (Windows)
myenv\Scripts\activate.bat

# Activate with full path (Linux/Mac)
source myenv/bin/activate

# Verify activation
which python  # Should show myenv path
python -c "import sys; print(sys.prefix)"  # Should show myenv path
```

**Windows-Specific Issues**:
```bash
# If PowerShell execution policy blocks activation
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser

# Alternative activation method
python -m venv myenv
myenv\Scripts\python.exe -m pip install --upgrade pip
```

### Issue 3: pip Installation Failures
**Error**: `Microsoft Visual C++ 14.0 is required` or compilation errors

**Symptoms**:
- Packages fail to install with compilation errors
- Missing compiler tools
- Binary wheel not available

**Solution**:
```bash
# Upgrade pip and build tools
python -m pip install --upgrade pip setuptools wheel

# Install binary-only packages (avoid compilation)
pip install --only-binary=all -r requirements.txt

# For Windows - install Visual C++ Build Tools
# Download from: https://visualstudio.microsoft.com/visual-cpp-build-tools/

# Alternative: Install packages individually
pip install python-dotenv
pip install pymongo
pip install pinecone-client
pip install openai
pip install mysql-connector-python
pip install flask flask-cors
pip install pandas numpy
```

**Package-Specific Solutions**:
```bash
# For numpy/pandas issues
pip install --upgrade numpy pandas

# For MySQL connector issues
pip install PyMySQL  # Alternative to mysql-connector-python

# For Pinecone issues
pip install pinecone-client --upgrade

# For OpenAI issues
pip install openai --upgrade
```

---

## 🗄️ Database Connection Issues

### Issue 4: MongoDB Connection Failures
**Error**: `ServerSelectionTimeoutError` or `Authentication failed`

**Symptoms**:
- Cannot connect to MongoDB
- Timeout errors
- Authentication failures

**Solution**:
```bash
# Check connection string format
# Correct format: mongodb+srv://username:<EMAIL>/database

# Test connection manually
python -c "
import pymongo
client = pymongo.MongoClient('your_connection_string')
try:
    client.admin.command('ping')
    print('✅ MongoDB connection successful')
except Exception as e:
    print(f'❌ MongoDB connection failed: {e}')
"

# Common fixes:
# 1. URL encode special characters in password
# 2. Check IP whitelist in MongoDB Atlas
# 3. Verify database name exists
# 4. Check network connectivity
```

**Environment Variable Issues**:
```bash
# Check environment variables
python -c "
import os
from dotenv import load_dotenv
load_dotenv()
print('MONGODB_CONNECTION_STRING:', os.getenv('MONGODB_CONNECTION_STRING'))
print('MONGODB_DATABASE:', os.getenv('MONGODB_DATABASE'))
"

# Fix .env file format (no spaces around =)
MONGODB_CONNECTION_STRING=mongodb+srv://user:<EMAIL>/db
MONGODB_DATABASE=your_database
```

### Issue 5: MySQL Connection Failures
**Error**: `Access denied` or `Can't connect to MySQL server`

**Symptoms**:
- MySQL connection refused
- Authentication errors
- Service not running

**Solution**:
```bash
# Check MySQL service status
# Windows:
net start mysql
# or
services.msc  # Check MySQL service

# Linux:
sudo systemctl status mysql
sudo systemctl start mysql

# Mac:
brew services list | grep mysql
brew services start mysql

# Test connection
mysql -u root -p -e "SHOW DATABASES;"

# Create database if missing
mysql -u root -p -e "CREATE DATABASE IF NOT EXISTS autoqa_results;"
```

**Permission Issues**:
```bash
# Grant permissions to user
mysql -u root -p -e "
GRANT ALL PRIVILEGES ON autoqa_results.* TO 'your_user'@'localhost';
FLUSH PRIVILEGES;
"

# Reset MySQL root password (if needed)
# Windows (XAMPP): Use XAMPP control panel
# Linux: sudo mysql_secure_installation
```

### Issue 6: Pinecone Connection Failures
**Error**: `Unauthorized` or `Index not found`

**Symptoms**:
- API key authentication fails
- Index doesn't exist
- Wrong environment/region

**Solution**:
```bash
# Verify API key format (should start with 'pcsk_')
python -c "
import os
from dotenv import load_dotenv
load_dotenv()
api_key = os.getenv('PINECONE_API_KEY')
print(f'API Key format: {api_key[:10]}...' if api_key else 'API Key not set')
print(f'Starts with pcsk_: {api_key.startswith(\"pcsk_\") if api_key else False}')
"

# Test Pinecone connection
python -c "
from pinecone import Pinecone
import os
from dotenv import load_dotenv
load_dotenv()

pc = Pinecone(api_key=os.getenv('PINECONE_API_KEY'))
try:
    indexes = pc.list_indexes()
    print(f'✅ Pinecone connected. Indexes: {[idx.name for idx in indexes]}')
except Exception as e:
    print(f'❌ Pinecone connection failed: {e}')
"

# Create missing indexes
python scripts/setup_pinecone_index.py
```

---

## 🔐 Environment Variables Issues

### Issue 7: Environment Variables Not Loading
**Error**: `None` values for environment variables or `KeyError`

**Symptoms**:
- Environment variables return None
- Configuration not found
- API keys not recognized

**Solution**:
```bash
# Check .env file exists and format
ls -la .env
cat .env

# Verify .env file format (no spaces around =, no quotes unless needed)
# Correct:
OPENAI_API_KEY=sk-proj-abc123
MYSQL_PASSWORD=mypassword

# Incorrect:
OPENAI_API_KEY = sk-proj-abc123
MYSQL_PASSWORD="mypassword"

# Test environment loading
python -c "
from dotenv import load_dotenv
import os

print('Before load_dotenv:')
print('OPENAI_API_KEY:', os.getenv('OPENAI_API_KEY'))

load_dotenv()
print('After load_dotenv:')
print('OPENAI_API_KEY:', os.getenv('OPENAI_API_KEY'))
"
```

**Path Issues**:
```bash
# Ensure .env file is in correct location (project root)
pwd  # Should be in autoqa project directory
ls -la .env  # Should exist

# Load .env with explicit path
python -c "
from dotenv import load_dotenv
from pathlib import Path

env_path = Path('.') / '.env'
print(f'Loading .env from: {env_path.absolute()}')
load_dotenv(env_path)
"
```

### Issue 8: API Key Format Issues
**Error**: `Invalid API key` or authentication failures

**Symptoms**:
- API calls fail with authentication errors
- Keys not recognized by services

**Solution**:
```bash
# Verify API key formats
python -c "
import os
from dotenv import load_dotenv
load_dotenv()

# Check OpenAI API key
openai_key = os.getenv('OPENAI_API_KEY')
print(f'OpenAI key format: {openai_key[:10]}...' if openai_key else 'OpenAI key not set')
print(f'Starts with sk-: {openai_key.startswith(\"sk-\") if openai_key else False}')

# Check Pinecone API key
pinecone_key = os.getenv('PINECONE_API_KEY')
print(f'Pinecone key format: {pinecone_key[:10]}...' if pinecone_key else 'Pinecone key not set')
print(f'Starts with pcsk_: {pinecone_key.startswith(\"pcsk_\") if pinecone_key else False}')
"

# Test API keys
python -c "
import openai
import os
from dotenv import load_dotenv
load_dotenv()

# Test OpenAI
try:
    client = openai.OpenAI(api_key=os.getenv('OPENAI_API_KEY'))
    models = client.models.list()
    print('✅ OpenAI API key valid')
except Exception as e:
    print(f'❌ OpenAI API key invalid: {e}')
"
```

---

## 📦 Package and Import Issues

### Issue 9: Import Errors
**Error**: `ModuleNotFoundError` or `ImportError`

**Symptoms**:
- Cannot import required modules
- Package not found errors
- Version compatibility issues

**Solution**:
```bash
# Verify virtual environment is activated
which python  # Should show myenv path

# Check installed packages
pip list

# Install missing packages
pip install -r requirements.txt

# Test critical imports
python -c "
try:
    import pymongo
    print('✅ pymongo imported')
except ImportError as e:
    print(f'❌ pymongo import failed: {e}')

try:
    import pinecone
    print('✅ pinecone imported')
except ImportError as e:
    print(f'❌ pinecone import failed: {e}')

try:
    import openai
    print('✅ openai imported')
except ImportError as e:
    print(f'❌ openai import failed: {e}')

try:
    import mysql.connector
    print('✅ mysql.connector imported')
except ImportError as e:
    print(f'❌ mysql.connector import failed: {e}')

try:
    import flask
    print('✅ flask imported')
except ImportError as e:
    print(f'❌ flask import failed: {e}')
"
```

**Path Issues**:
```bash
# Check Python path includes src directory
python -c "
import sys
from pathlib import Path
project_root = Path('.').absolute()
src_path = project_root / 'src'
print(f'Project root: {project_root}')
print(f'Src path: {src_path}')
print(f'Src in sys.path: {str(src_path) in sys.path}')

# Add src to path if needed
sys.path.append(str(src_path))
"
```

### Issue 10: Version Conflicts
**Error**: Package version conflicts or compatibility issues

**Symptoms**:
- Packages install but don't work correctly
- Version mismatch warnings
- Functionality missing

**Solution**:
```bash
# Check package versions
pip list | grep -E "(pymongo|pinecone|openai|mysql|flask)"

# Update to specific versions from requirements.txt
pip install pymongo==4.6.1
pip install pinecone-client==3.0.0
pip install openai==1.12.0
pip install mysql-connector-python==8.3.0
pip install Flask==3.0.0

# Resolve conflicts
pip install --upgrade --force-reinstall -r requirements.txt
```

---

## 🧪 Testing and Validation Issues

### Issue 11: Tests Failing
**Error**: Test failures in `test_complete_implementation.py`

**Symptoms**:
- Database connection tests fail
- Web console tests fail
- Data consistency tests fail

**Solution**:
```bash
# Run tests with verbose output
python test/test_complete_implementation.py

# Test individual components
python -c "
from src.database.connections import DatabaseConnections
db = DatabaseConnections()

# Test each connection individually
try:
    mongodb = db.connect_mongodb()
    print('✅ MongoDB connection test passed')
except Exception as e:
    print(f'❌ MongoDB connection test failed: {e}')

try:
    mysql = db.connect_mysql()
    print('✅ MySQL connection test passed')
except Exception as e:
    print(f'❌ MySQL connection test failed: {e}')

try:
    pinecone_index = db.get_pinecone_index('autoqa-transcripts')
    print('✅ Pinecone connection test passed')
except Exception as e:
    print(f'❌ Pinecone connection test failed: {e}')
"
```

### Issue 12: Web Console Not Working
**Error**: Web server fails to start or endpoints not responding

**Symptoms**:
- Flask server won't start
- 404 errors on endpoints
- CORS issues

**Solution**:
```bash
# Test Flask installation
python -c "
import flask
print(f'Flask version: {flask.__version__}')
"

# Start web server with debug output
python web/qa_results_server.py

# Test endpoints manually
curl http://localhost:5000/health
curl http://localhost:5000/api/qa-results

# Check for port conflicts
netstat -an | grep :5000  # Windows
lsof -i :5000  # Linux/Mac
```

---

## 🚨 Emergency Recovery

### Complete System Reset
If all else fails, perform a complete reset:

```bash
# 1. Stop all processes
# Kill any running Python processes

# 2. Remove virtual environment
rm -rf myenv

# 3. Recreate environment
python -m venv myenv
myenv\Scripts\activate  # Windows
source myenv/bin/activate  # Linux/Mac

# 4. Reinstall everything
python -m pip install --upgrade pip
pip install -r requirements.txt

# 5. Reconfigure environment
cp .env.example .env
# Edit .env with correct values

# 6. Test step by step
python -c "from dotenv import load_dotenv; load_dotenv(); print('Environment loaded')"
python -c "from src.database.connections import DatabaseConnections; print('Imports working')"
python test/test_complete_implementation.py
```

### Rollback to Previous Working State
```bash
# Return to original machine/environment
# Restore from backup:
cp .env.backup .env
mysql -u root -p autoqa_results < backup_mysql_autoqa.sql

# Verify original system works
python test/test_complete_implementation.py
```

---

## 📞 Getting Additional Help

### Diagnostic Information to Collect
When seeking help, provide:

```bash
# System information
python --version
pip --version
which python
echo $PATH

# Package versions
pip list

# Environment variables (without sensitive values)
python -c "
import os
from dotenv import load_dotenv
load_dotenv()
vars_to_check = ['MONGODB_DATABASE', 'PINECONE_INDEX_NAME', 'OPENAI_MODEL', 'MYSQL_HOST', 'MYSQL_DATABASE']
for var in vars_to_check:
    value = os.getenv(var)
    print(f'{var}: {\"SET\" if value else \"NOT SET\"}')
"

# Error logs
cat logs/autoqa.log  # If exists
```

### Common Log Locations
- Application logs: `logs/autoqa.log`
- MySQL logs: Check MySQL error log location
- Python error output: Terminal/console output
- Web server logs: Flask debug output

---

**Troubleshooting Guide Version**: 1.0  
**Last Updated**: December 2024  
**Compatible with**: AutoQA v2.0+ (Production Ready)
