# 🚀 AutoQA Project Deployment Guide

## 📋 Overview

This comprehensive guide provides step-by-step instructions for migrating the AutoQA project to a new machine. The guide is designed for both technical and non-technical users and includes verification procedures, troubleshooting, and rollback options.

**Estimated Total Time**: 2-4 hours (depending on data size and internet speed)

---

## ✅ Pre-Migration Checklist

### 1. Current System Documentation (15 minutes)

#### 1.1 Verify Requirements File
```bash
# Check current requirements.txt is complete
cat requirements.txt

# Generate current environment snapshot (if needed)
pip freeze > current_environment.txt
```

#### 1.2 Document Current Configuration
Create a backup of your current configuration:

```bash
# Copy environment configuration
cp .env .env.backup

# Document current Python version
python --version > system_info.txt

# Document current virtual environment
echo "Virtual Environment: $(which python)" >> system_info.txt

# Document current working directory
echo "Project Root: $(pwd)" >> system_info.txt
```

#### 1.3 Verify Current System Status
Run the complete implementation test to ensure everything is working:

```bash
# Activate virtual environment
myenv\Scripts\activate  # Windows
# source myenv/bin/activate  # Linux/Mac

# Run comprehensive test
python test/test_complete_implementation.py
```

**Expected Output**: All tests should pass (✅ PASSED)

### 2. Data Backup (30-60 minutes)

#### 2.1 MySQL Database Backup
```bash
# Create MySQL backup
mysqldump -u root -p autoqa_results > backup_mysql_autoqa.sql

# Verify backup file
ls -la backup_mysql_autoqa.sql
```

#### 2.2 Pinecone Index Documentation
```bash
# Document Pinecone indexes
python -c "
from src.database.connections import DatabaseConnections
db = DatabaseConnections()

# Check autoqa-transcripts index
transcripts_index = db.get_pinecone_index('autoqa-transcripts')
stats = transcripts_index.describe_index_stats()
print(f'autoqa-transcripts: {stats.total_vector_count} vectors')

# Check autoqa-qa-results index  
qa_index = db.get_pinecone_index('autoqa-qa-results')
stats = qa_index.describe_index_stats()
print(f'autoqa-qa-results: {stats.total_vector_count} vectors')
"
```

#### 2.3 Configuration Files Backup
```bash
# Create backup directory
mkdir -p backup/$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="backup/$(date +%Y%m%d_%H%M%S)"

# Backup critical files
cp .env $BACKUP_DIR/
cp requirements.txt $BACKUP_DIR/
cp -r config/ $BACKUP_DIR/
cp -r logs/ $BACKUP_DIR/
cp QA_Insight_Prompt.txt $BACKUP_DIR/

# Create backup manifest
echo "Backup created: $(date)" > $BACKUP_DIR/backup_manifest.txt
echo "Source machine: $(hostname)" >> $BACKUP_DIR/backup_manifest.txt
echo "Python version: $(python --version)" >> $BACKUP_DIR/backup_manifest.txt
```

### 3. Environment Variables Documentation (10 minutes)

Create a secure documentation of your environment variables:

```bash
# Create environment template (without sensitive data)
python -c "
import os
from dotenv import load_dotenv
load_dotenv()

# List all AutoQA environment variables (without values)
env_vars = [
    'MONGO_URI', 'MONGODB_CONNECTION_STRING', 'MONGODB_DATABASE',
    'PINECONE_API_KEY', 'PINECONE_INDEX_NAME', 'PINECONE_HOST',
    'OPENAI_API_KEY', 'OPENAI_MODEL',
    'MYSQL_HOST', 'MYSQL_DATABASE', 'MYSQL_USERNAME', 'MYSQL_PASSWORD',
    'DB_HOST', 'DB_NAME', 'DB_USER', 'DB_PASS'
]

print('Environment Variables Required:')
for var in env_vars:
    value = os.getenv(var)
    status = '✅ SET' if value else '❌ MISSING'
    print(f'  {var}: {status}')
"
```

---

## 🔧 Step-by-Step Migration Process

### Step 1: New Machine Setup (30 minutes)

#### 1.1 System Requirements Verification
```bash
# Check Python version (3.9+ required)
python --version

# Check pip
pip --version

# Check Git (if cloning from repository)
git --version
```

**Required Versions**:
- Python: 3.9 or higher
- pip: Latest version
- Git: Any recent version

#### 1.2 Project Transfer
**Option A: From Git Repository**
```bash
git clone <your-repository-url> autoqa
cd autoqa
```

**Option B: From Backup/Copy**
```bash
# Copy project files to new machine
# Ensure all files are transferred including hidden files (.env, .gitignore)
```

#### 1.3 Directory Structure Verification
```bash
# Verify project structure
ls -la

# Expected directories and files:
# ├── src/
# ├── scripts/
# ├── web/
# ├── test/
# ├── docs/
# ├── config/
# ├── logs/
# ├── requirements.txt
# ├── .env.example
# └── README.md
```

### Step 2: Python Virtual Environment Setup (15 minutes)

#### 2.1 Create Virtual Environment
```bash
# Create virtual environment named 'myenv'
python -m venv myenv

# Activate virtual environment
# Windows:
myenv\Scripts\activate

# Linux/Mac:
source myenv/bin/activate

# Verify activation
which python
# Should show path to myenv/Scripts/python or myenv/bin/python
```

#### 2.2 Upgrade pip
```bash
# Upgrade pip to latest version
python -m pip install --upgrade pip

# Verify pip version
pip --version
```

### Step 3: Dependencies Installation (20-30 minutes)

#### 3.1 Install Requirements
```bash
# Install all dependencies from requirements.txt
pip install -r requirements.txt

# Verify critical packages
python -c "
import pymongo
import pinecone
import openai
import mysql.connector
import flask
import pandas
print('✅ All critical packages imported successfully')
"
```

#### 3.2 Handle Installation Issues
If you encounter installation errors:

```bash
# For Windows users with compilation issues:
pip install --only-binary=all -r requirements.txt

# For specific package issues:
pip install --upgrade setuptools wheel

# Retry installation
pip install -r requirements.txt
```

### Step 4: Database Configuration (45-60 minutes)

#### 4.1 Environment Variables Setup
```bash
# Copy environment template
cp .env.example .env

# Edit .env file with your actual values
# Use your preferred text editor (notepad, nano, vim, etc.)
notepad .env  # Windows
# nano .env   # Linux/Mac
```

**Required Environment Variables**:
```bash
# MongoDB Configuration
MONGODB_CONNECTION_STRING=mongodb+srv://username:<EMAIL>/database
MONGODB_DATABASE=your_database_name

# Pinecone Configuration  
PINECONE_API_KEY=pcsk_your_api_key_here
PINECONE_INDEX_NAME=autoqa-transcripts
PINECONE_QA_RESULTS_INDEX=autoqa-qa-results
PINECONE_HOST=https://your-index-xxxxx.svc.environment.pinecone.io

# OpenAI Configuration
OPENAI_API_KEY=sk-proj-your_api_key_here
OPENAI_MODEL=gpt-4o-mini

# MySQL Configuration
MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_DATABASE=autoqa_results
MYSQL_USERNAME=root
MYSQL_PASSWORD=your_mysql_password
```

#### 4.2 MySQL Database Setup
```bash
# Start MySQL service (if not running)
# Windows (XAMPP):
# Start XAMPP Control Panel and start MySQL

# Windows (MySQL Service):
net start mysql

# Linux:
sudo systemctl start mysql

# Mac:
brew services start mysql
```

Create the database and tables:
```bash
# Run MySQL schema setup
python scripts/setup_mysql_schema.py

# Expected output:
# ✅ Database 'autoqa_results' created successfully
# ✅ Table 'qa_results' created successfully
# ✅ Table 'processing_log' created successfully
# ✅ Table 'system_metrics' created successfully
```

#### 4.3 Restore MySQL Data (if migrating existing data)
```bash
# Restore from backup (if you have existing data)
mysql -u root -p autoqa_results < backup_mysql_autoqa.sql
```

### Step 5: Pinecone Configuration (15 minutes)

#### 5.1 Verify Pinecone Indexes
```bash
# Check if indexes exist
python -c "
from src.database.connections import DatabaseConnections
db = DatabaseConnections()

try:
    # Check autoqa-transcripts index
    transcripts_index = db.get_pinecone_index('autoqa-transcripts')
    stats = transcripts_index.describe_index_stats()
    print(f'✅ autoqa-transcripts: {stats.total_vector_count} vectors')
except Exception as e:
    print(f'❌ autoqa-transcripts: {e}')

try:
    # Check autoqa-qa-results index
    qa_index = db.get_pinecone_index('autoqa-qa-results')
    stats = qa_index.describe_index_stats()
    print(f'✅ autoqa-qa-results: {stats.total_vector_count} vectors')
except Exception as e:
    print(f'❌ autoqa-qa-results: {e}')
"
```

#### 5.2 Create Indexes (if they don't exist)
```bash
# Create Pinecone indexes
python scripts/setup_pinecone_index.py

# Expected output:
# ✅ Index 'autoqa-transcripts' created successfully
# ✅ Index 'autoqa-qa-results' created successfully
```

---

## 🧪 Verification and Testing

### Step 6: System Connectivity Tests (20 minutes)

#### 6.1 Database Connection Tests
```bash
# Test all database connections
python -c "
from src.database.connections import DatabaseConnections

db = DatabaseConnections()

# Test MongoDB
try:
    mongodb = db.connect_mongodb()
    print('✅ MongoDB connection successful')
except Exception as e:
    print(f'❌ MongoDB connection failed: {e}')

# Test MySQL
try:
    mysql = db.connect_mysql()
    print('✅ MySQL connection successful')
except Exception as e:
    print(f'❌ MySQL connection failed: {e}')

# Test Pinecone
try:
    pinecone_index = db.get_pinecone_index('autoqa-transcripts')
    print('✅ Pinecone connection successful')
except Exception as e:
    print(f'❌ Pinecone connection failed: {e}')

db.close_connections()
"
```

#### 6.2 API Keys Validation
```bash
# Test OpenAI API
python -c "
import openai
import os
from dotenv import load_dotenv

load_dotenv()

try:
    client = openai.OpenAI(api_key=os.getenv('OPENAI_API_KEY'))
    # Test with a simple request
    response = client.models.list()
    print('✅ OpenAI API key valid')
except Exception as e:
    print(f'❌ OpenAI API key invalid: {e}')
"
```

### Step 7: End-to-End Functionality Validation (30 minutes)

#### 7.1 Run Complete Implementation Test
```bash
# Run comprehensive system test
python test/test_complete_implementation.py
```

**Expected Output**:
```
📊 COMPLETE IMPLEMENTATION TEST SUMMARY:
  MySQL Storage: ✅ PASSED
  Web Console: ✅ PASSED  
  Data Consistency: ✅ PASSED

🎉 ALL TESTS PASSED!
✅ MySQL storage implementation is working correctly!
✅ Web console displays AutoQA data properly!
✅ Data is consistent between MySQL and Pinecone!
```

#### 7.2 Test Web Console
```bash
# Start web server
python web/qa_results_server.py

# In another terminal/browser, test endpoints:
# http://localhost:5000/health
# http://localhost:5000/api/qa-results
```

#### 7.3 Test Parallel Processing (if you have data)
```bash
# Test with a small batch
python scripts/autoqa_parallel_processor.py --limit 3 --workers 2

# Expected output:
# 🚀 Starting parallel QA processing (limit: 3)
# Processing 3 calls with 2 workers
# ✅ [call_id]: [score] ([time]s)
# 📊 Parallel Processing Summary:
#   Success rate: 100.0%
```

### Step 8: Performance Benchmarking (15 minutes)

#### 8.1 Database Performance Test
```bash
# Test database query performance
python -c "
import time
from src.database.connections import DatabaseConnections

db = DatabaseConnections()

# Test MySQL query speed
start_time = time.time()
mysql_conn = db.connect_mysql()
cursor = mysql_conn.cursor()
cursor.execute('SELECT COUNT(*) FROM qa_results')
result = cursor.fetchone()
mysql_time = time.time() - start_time
print(f'MySQL query time: {mysql_time:.3f}s')

# Test Pinecone query speed
start_time = time.time()
pinecone_index = db.get_pinecone_index('autoqa-transcripts')
query_result = pinecone_index.query(
    vector=[0.0] * 1536,
    top_k=10,
    include_metadata=True
)
pinecone_time = time.time() - start_time
print(f'Pinecone query time: {pinecone_time:.3f}s')

db.close_connections()
"
```

**Expected Performance**:
- MySQL queries: < 0.1s
- Pinecone queries: < 0.5s
- Web console response: < 1s

---

## 📚 Documentation Requirements

### Step 9: Update Project Documentation (10 minutes)

#### 9.1 Create Deployment Record
```bash
# Create deployment log
echo "AutoQA Deployment Record" > deployment_record.txt
echo "========================" >> deployment_record.txt
echo "Deployment Date: $(date)" >> deployment_record.txt
echo "Target Machine: $(hostname)" >> deployment_record.txt
echo "Python Version: $(python --version)" >> deployment_record.txt
echo "Virtual Environment: myenv" >> deployment_record.txt
echo "Project Location: $(pwd)" >> deployment_record.txt
echo "" >> deployment_record.txt
echo "Database Connections:" >> deployment_record.txt
echo "- MongoDB: Connected" >> deployment_record.txt
echo "- MySQL: Connected" >> deployment_record.txt  
echo "- Pinecone: Connected" >> deployment_record.txt
echo "- OpenAI: Connected" >> deployment_record.txt
```

#### 9.2 Update System Documentation
Update the main documentation with new machine details:

```bash
# Update system documentation
cp docs/AUTOQA_SYSTEM_DOCUMENTATION.md docs/AUTOQA_SYSTEM_DOCUMENTATION.md.backup

# Add deployment information to documentation
echo "" >> docs/AUTOQA_SYSTEM_DOCUMENTATION.md
echo "## 🚀 Current Deployment" >> docs/AUTOQA_SYSTEM_DOCUMENTATION.md
echo "- **Deployed**: $(date)" >> docs/AUTOQA_SYSTEM_DOCUMENTATION.md
echo "- **Machine**: $(hostname)" >> docs/AUTOQA_SYSTEM_DOCUMENTATION.md
echo "- **Python**: $(python --version)" >> docs/AUTOQA_SYSTEM_DOCUMENTATION.md
echo "- **Environment**: myenv" >> docs/AUTOQA_SYSTEM_DOCUMENTATION.md
```

---

## 🔧 Troubleshooting Guide

### Common Migration Issues

#### Issue 1: Python Version Compatibility
**Problem**: "Python version not supported"
**Solution**:
```bash
# Check Python version
python --version

# If version < 3.9, install Python 3.9+
# Windows: Download from python.org
# Linux: sudo apt install python3.9
# Mac: brew install python@3.9
```

#### Issue 2: Virtual Environment Issues
**Problem**: Virtual environment not activating
**Solution**:
```bash
# Delete and recreate virtual environment
rm -rf myenv
python -m venv myenv

# Windows activation
myenv\Scripts\activate

# Linux/Mac activation  
source myenv/bin/activate
```

#### Issue 3: Package Installation Failures
**Problem**: pip install failures
**Solution**:
```bash
# Upgrade pip and setuptools
python -m pip install --upgrade pip setuptools wheel

# Install packages individually
pip install python-dotenv
pip install pymongo
pip install pinecone-client
pip install openai
pip install mysql-connector-python
pip install flask flask-cors
```

#### Issue 4: Database Connection Failures
**Problem**: Cannot connect to databases
**Solution**:

**MongoDB**:
```bash
# Check connection string format
# Correct: mongodb+srv://user:<EMAIL>/db
# Check firewall/network access
```

**MySQL**:
```bash
# Start MySQL service
net start mysql  # Windows
sudo systemctl start mysql  # Linux

# Check credentials and database exists
mysql -u root -p -e "SHOW DATABASES;"
```

**Pinecone**:
```bash
# Verify API key format (should start with 'pcsk_')
# Check index names exist in Pinecone console
```

#### Issue 5: Environment Variables Not Loading
**Problem**: Environment variables not found
**Solution**:
```bash
# Check .env file exists and has correct format
ls -la .env
cat .env

# Verify no extra spaces or quotes
# Format: VARIABLE_NAME=value (no spaces around =)

# Test environment loading
python -c "
from dotenv import load_dotenv
import os
load_dotenv()
print('OPENAI_API_KEY:', 'SET' if os.getenv('OPENAI_API_KEY') else 'NOT SET')
"
```

---

## 🔄 Rollback Procedures

### Emergency Rollback (if migration fails)

#### 1. Immediate Rollback Steps
```bash
# Stop all AutoQA processes
# Kill web server (Ctrl+C)
# Kill any running parallel processors

# Return to original machine/environment
# Restore from backup if needed
```

#### 2. Data Recovery
```bash
# If data was modified during failed migration:

# Restore MySQL from backup
mysql -u root -p autoqa_results < backup_mysql_autoqa.sql

# Pinecone data should be unchanged (cloud-based)
# MongoDB data should be unchanged (read-only for AutoQA)
```

#### 3. Configuration Recovery
```bash
# Restore original configuration
cp .env.backup .env

# Verify original system still works
python test/test_complete_implementation.py
```

---

## ✅ Post-Migration Checklist

### Final Verification Steps

- [ ] All database connections working
- [ ] Environment variables properly set
- [ ] Virtual environment activated and working
- [ ] All dependencies installed correctly
- [ ] Web console accessible and functional
- [ ] Parallel processing working (if tested)
- [ ] Performance benchmarks acceptable
- [ ] Documentation updated
- [ ] Backup procedures documented
- [ ] Team notified of new deployment location

### Success Criteria

✅ **Migration Successful** when:
- All tests in `test_complete_implementation.py` pass
- Web console loads and displays data correctly
- Database queries return expected results
- API endpoints respond within acceptable time limits
- No error messages in logs during normal operation

---

## 📞 Support and Next Steps

### Getting Help
- Review troubleshooting section above
- Check logs in `logs/` directory for detailed error messages
- Verify environment variables are correctly set
- Ensure all services (MySQL, MongoDB, Pinecone) are accessible

### Recommended Next Steps After Migration
1. **Run a small test batch** to verify parallel processing
2. **Set up monitoring** for the new environment
3. **Schedule regular backups** of MySQL data
4. **Document any environment-specific configurations**
5. **Train team members** on new deployment location

---

---

## 🤖 Automated Deployment Option

### Quick Automated Setup (30-45 minutes)

For users who prefer automated deployment, use the deployment automation script:

```bash
# Run automated deployment
python scripts/deploy_autoqa.py

# Follow the prompts and instructions
# The script will:
# 1. Check prerequisites
# 2. Create virtual environment
# 3. Install dependencies
# 4. Set up configuration files
# 5. Validate installation
```

**After automated deployment**:
1. Edit `.env` file with your actual credentials
2. Run validation: `python scripts/validate_requirements.py`
3. Test system: `python test/test_complete_implementation.py`

### Requirements Validation

Before or after deployment, validate your setup:

```bash
# Activate virtual environment
myenv\Scripts\activate  # Windows
source myenv/bin/activate  # Linux/Mac

# Run validation script
python scripts/validate_requirements.py

# Expected output:
# 🎉 ALL VALIDATION CHECKS PASSED!
# ✅ AutoQA project is ready for deployment
```

---

## 📋 Additional Resources

### Related Documentation
- **[Deployment Checklist](DEPLOYMENT_CHECKLIST.md)**: Step-by-step checklist
- **[Migration Troubleshooting](MIGRATION_TROUBLESHOOTING.md)**: Common issues and solutions
- **[System Documentation](AUTOQA_SYSTEM_DOCUMENTATION.md)**: Complete system overview

### Automation Scripts
- **`scripts/deploy_autoqa.py`**: Automated deployment script
- **`scripts/validate_requirements.py`**: Requirements validation
- **`scripts/setup_mysql_schema.py`**: Database setup
- **`scripts/setup_pinecone_index.py`**: Pinecone index creation

### Quick Reference Commands
```bash
# Activate virtual environment
myenv\Scripts\activate  # Windows
source myenv/bin/activate  # Linux/Mac

# Validate installation
python scripts/validate_requirements.py

# Test system
python test/test_complete_implementation.py

# Start web console
python web/qa_results_server.py

# Run parallel processing
python scripts/autoqa_parallel_processor.py --limit 3 --workers 2
```

---

**Migration Guide Version**: 1.0
**Last Updated**: December 2024
**Compatible with**: AutoQA v2.0+ (Production Ready)
