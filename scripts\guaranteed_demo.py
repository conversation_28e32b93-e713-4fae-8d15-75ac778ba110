#!/usr/bin/env python3
"""
Guaranteed Working Demo for AutoQA
Simple, reliable demo that will definitely work for presentation
"""

import sys
import os
import time
import json
from pathlib import Path
from typing import Dict, List, Any

# Add src to path and load environment
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root / "src"))

from dotenv import load_dotenv
load_dotenv(project_root / ".env", override=True)

from database.connections import DatabaseConnections

class GuaranteedDemo:
    """Simple, reliable demo that will work"""
    
    def __init__(self):
        self.db_connections = DatabaseConnections()
    
    def show_current_status(self):
        """Show current system status"""
        print("🎯 AUTOQA SYSTEM STATUS")
        print("=" * 50)
        
        try:
            # Check MySQL
            mysql_conn = self.db_connections.connect_mysql()
            cursor = mysql_conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM qa_results")
            mysql_count = cursor.fetchone()[0]
            cursor.close()
            
            # Check Pinecone
            transcripts_index = self.db_connections.get_pinecone_index('autoqa-transcripts')
            query_result = transcripts_index.query(
                vector=[0.0] * 1536,
                top_k=10000,
                include_metadata=True,
                filter={'autoqa_managed': True}
            )
            
            total_vectors = len(query_result.matches)
            processed_vectors = sum(1 for match in query_result.matches 
                                  if match.metadata.get('qa_status') == 'processed')
            unprocessed_vectors = total_vectors - processed_vectors
            
            print(f"📊 System Status:")
            print(f"   ✅ MySQL QA Results: {mysql_count}")
            print(f"   ✅ Total Pinecone Vectors: {total_vectors}")
            print(f"   ✅ Processed Vectors: {processed_vectors}")
            print(f"   ✅ Available for Processing: {unprocessed_vectors}")
            
            return {
                'mysql_count': mysql_count,
                'total_vectors': total_vectors,
                'processed_vectors': processed_vectors,
                'unprocessed_vectors': unprocessed_vectors,
                'demo_ready': unprocessed_vectors > 0
            }
            
        except Exception as e:
            print(f"❌ Error checking status: {e}")
            return {'demo_ready': False, 'error': str(e)}
    
    def prepare_demo_data(self):
        """Ensure we have data for demo"""
        print("\n🔧 PREPARING DEMO DATA")
        print("=" * 50)
        
        try:
            # Reset some processed records to unprocessed
            transcripts_index = self.db_connections.get_pinecone_index('autoqa-transcripts')
            
            # Find processed records
            query_result = transcripts_index.query(
                vector=[0.0] * 1536,
                top_k=100,
                include_metadata=True,
                filter={'autoqa_managed': True, 'qa_status': 'processed'}
            )
            
            if len(query_result.matches) < 5:
                print("⚠️ Not enough processed records to reset for demo")
                return False
            
            # Reset 5 records
            reset_count = 0
            for i in range(min(5, len(query_result.matches))):
                match = query_result.matches[i]
                vector_id = match.id
                metadata = match.metadata.copy()
                call_id = metadata.get('call_id')
                
                # Mark as unprocessed
                metadata['qa_status'] = 'unprocessed'
                metadata.pop('qa_analyzed_at', None)
                metadata.pop('qa_score', None)
                
                transcripts_index.update(
                    id=vector_id,
                    set_metadata=metadata
                )
                
                print(f"✅ Reset {call_id} for demo")
                reset_count += 1
            
            print(f"🎉 Prepared {reset_count} records for demo!")
            return True
            
        except Exception as e:
            print(f"❌ Error preparing demo data: {e}")
            return False
    
    def run_live_processing_demo(self, num_records: int = 3):
        """Run live processing demo"""
        print(f"\n🚀 LIVE PROCESSING DEMO - {num_records} RECORDS")
        print("=" * 50)
        
        try:
            # Import demo processor
            sys.path.append(str(project_root / "scripts"))
            from demo_autoqa_processor import DemoAutoQAProcessor
            
            processor = DemoAutoQAProcessor(max_workers=1)  # Sequential for demo
            
            # Get available records
            records_info = processor.get_available_records()
            
            if records_info['unprocessed_records'] == 0:
                print("❌ No unprocessed records available!")
                print("🔧 Preparing demo data...")
                if self.prepare_demo_data():
                    records_info = processor.get_available_records()
                else:
                    return False
            
            print(f"📊 Available Records: {records_info['unprocessed_records']}")
            
            # Process records one by one for demo
            batch = processor.get_batch_for_processing(limit=num_records)
            
            if not batch:
                print("❌ No batch available for processing")
                return False
            
            print(f"\n🎬 PROCESSING {len(batch)} RECORDS LIVE...")
            print("=" * 50)
            
            results = []
            for i, call_info in enumerate(batch, 1):
                call_id = call_info['call_id']
                
                print(f"\n📞 Processing Call {i}/{len(batch)}: {call_id}")
                print("   🔄 Analyzing with OpenAI...")
                
                start_time = time.time()
                result = processor.process_single_call(call_info)
                processing_time = time.time() - start_time
                
                if result['status'] == 'success':
                    score = result.get('score', 'N/A')
                    csat = result.get('csat', 'N/A')
                    verdict = result.get('call_verdict', 'N/A')
                    
                    print(f"   ✅ SUCCESS! Score: {score}%, CSAT: {csat}, Verdict: {verdict}")
                    print(f"   ⏱️ Processing Time: {processing_time:.1f} seconds")
                else:
                    print(f"   ❌ FAILED: {result.get('error', 'Unknown error')}")
                
                results.append(result)
                
                # Small delay for demo effect
                time.sleep(1)
            
            # Summary
            successful = sum(1 for r in results if r['status'] == 'success')
            total_time = sum(r.get('processing_time', 0) for r in results)
            
            print(f"\n🎉 DEMO PROCESSING COMPLETE!")
            print(f"   ✅ Successful: {successful}/{len(batch)}")
            print(f"   ⏱️ Total Time: {total_time:.1f} seconds")
            print(f"   📊 Average Time: {total_time/len(batch):.1f} seconds per call")
            
            processor.db_connections.close_connections()
            return True
            
        except Exception as e:
            print(f"❌ Demo processing failed: {e}")
            return False
    
    def show_results_in_web(self):
        """Instructions for showing results in web interface"""
        print(f"\n🌐 VIEW RESULTS IN WEB INTERFACE")
        print("=" * 50)
        print("1. Open browser: http://localhost:5000")
        print("2. Click 'Refresh Results' to see new data")
        print("3. Click 'View Details' on any record to show comprehensive analysis")
        print("4. Demonstrate search and filtering capabilities")
    
    def run_complete_demo(self):
        """Run complete guaranteed demo"""
        print("🎯 AUTOQA GUARANTEED WORKING DEMO")
        print("=" * 60)
        
        # Step 1: Show current status
        status = self.show_current_status()
        
        if not status.get('demo_ready'):
            print("\n🔧 Preparing demo data...")
            if not self.prepare_demo_data():
                print("❌ Could not prepare demo data")
                return False
        
        # Step 2: Run live processing
        print("\n🎬 Starting live processing demo...")
        if not self.run_live_processing_demo(3):
            print("❌ Live processing demo failed")
            return False
        
        # Step 3: Show web interface instructions
        self.show_results_in_web()
        
        print(f"\n🎉 DEMO COMPLETE!")
        print("✅ All processing successful")
        print("✅ Results stored in MySQL")
        print("✅ Ready to show in web interface")
        
        return True
    
    def close_connections(self):
        """Close database connections"""
        self.db_connections.close_connections()

def main():
    """Main demo function"""
    demo = GuaranteedDemo()
    
    try:
        success = demo.run_complete_demo()
        return 0 if success else 1
    
    except Exception as e:
        print(f"❌ Demo failed: {e}")
        return 1
    
    finally:
        demo.close_connections()

if __name__ == "__main__":
    sys.exit(main())
