# 📋 AutoQA Deployment Checklist

## 🎯 Overview
This checklist ensures a complete and successful AutoQA deployment to a new machine. Check off each item as completed.

**Estimated Total Time**: 2-4 hours  
**Deployment Date**: _______________  
**Target Machine**: _______________  
**Deployed By**: _______________

---

## ✅ Pre-Migration Phase (45-75 minutes)

### 📋 Current System Documentation
- [ ] **Verify requirements.txt is complete** (5 min)
  - [ ] Run `cat requirements.txt` to review dependencies
  - [ ] Generate current environment snapshot: `pip freeze > current_environment.txt`
  
- [ ] **Document current configuration** (10 min)
  - [ ] Backup environment file: `cp .env .env.backup`
  - [ ] Document Python version: `python --version > system_info.txt`
  - [ ] Document virtual environment path
  - [ ] Document project root directory
  
- [ ] **Verify current system status** (15 min)
  - [ ] Activate virtual environment: `myenv\Scripts\activate`
  - [ ] Run complete test: `python test/test_complete_implementation.py`
  - [ ] Confirm all tests pass (✅ PASSED)

### 💾 Data Backup
- [ ] **MySQL Database Backup** (15 min)
  - [ ] Create backup: `mysqldump -u root -p autoqa_results > backup_mysql_autoqa.sql`
  - [ ] Verify backup file exists and has reasonable size
  - [ ] Test backup integrity (optional): `head -20 backup_mysql_autoqa.sql`

- [ ] **Pinecone Index Documentation** (10 min)
  - [ ] Document autoqa-transcripts vector count
  - [ ] Document autoqa-qa-results vector count
  - [ ] Note index configurations and settings

- [ ] **Configuration Files Backup** (10 min)
  - [ ] Create timestamped backup directory
  - [ ] Backup .env file
  - [ ] Backup requirements.txt
  - [ ] Backup config/ directory
  - [ ] Backup logs/ directory
  - [ ] Backup QA_Insight_Prompt.txt
  - [ ] Create backup manifest file

### 🔐 Environment Variables Documentation
- [ ] **Document required variables** (5 min)
  - [ ] List all MongoDB connection variables
  - [ ] List all Pinecone configuration variables
  - [ ] List all OpenAI API variables
  - [ ] List all MySQL connection variables
  - [ ] Verify all critical variables are set

---

## 🔧 Migration Phase (90-120 minutes)

### 🖥️ New Machine Setup
- [ ] **System Requirements Verification** (10 min)
  - [ ] Python 3.9+ installed: `python --version`
  - [ ] pip latest version: `pip --version`
  - [ ] Git available (if needed): `git --version`

- [ ] **Project Transfer** (15 min)
  - [ ] Clone from repository OR copy project files
  - [ ] Verify all directories present (src/, scripts/, web/, test/, docs/, config/, logs/)
  - [ ] Verify critical files present (requirements.txt, .env.example, README.md)

### 🐍 Python Environment Setup
- [ ] **Create Virtual Environment** (10 min)
  - [ ] Create: `python -m venv myenv`
  - [ ] Activate Windows: `myenv\Scripts\activate`
  - [ ] Activate Linux/Mac: `source myenv/bin/activate`
  - [ ] Verify activation: `which python`

- [ ] **Upgrade pip** (5 min)
  - [ ] Upgrade: `python -m pip install --upgrade pip`
  - [ ] Verify version: `pip --version`

### 📦 Dependencies Installation
- [ ] **Install Requirements** (20 min)
  - [ ] Install: `pip install -r requirements.txt`
  - [ ] Verify critical imports work:
    ```python
    import pymongo, pinecone, openai, mysql.connector, flask, pandas
    ```
  - [ ] Handle any installation errors (see troubleshooting guide)

### 🗄️ Database Configuration
- [ ] **Environment Variables Setup** (15 min)
  - [ ] Copy template: `cp .env.example .env`
  - [ ] Edit .env with actual values:
    - [ ] MONGODB_CONNECTION_STRING
    - [ ] MONGODB_DATABASE
    - [ ] PINECONE_API_KEY
    - [ ] PINECONE_INDEX_NAME
    - [ ] PINECONE_QA_RESULTS_INDEX
    - [ ] OPENAI_API_KEY
    - [ ] OPENAI_MODEL
    - [ ] MYSQL_HOST, MYSQL_DATABASE, MYSQL_USERNAME, MYSQL_PASSWORD

- [ ] **MySQL Database Setup** (20 min)
  - [ ] Start MySQL service
  - [ ] Run schema setup: `python scripts/setup_mysql_schema.py`
  - [ ] Verify tables created successfully
  - [ ] Restore data (if migrating): `mysql -u root -p autoqa_results < backup_mysql_autoqa.sql`

- [ ] **Pinecone Configuration** (15 min)
  - [ ] Verify indexes exist or create them: `python scripts/setup_pinecone_index.py`
  - [ ] Confirm autoqa-transcripts index accessible
  - [ ] Confirm autoqa-qa-results index accessible

---

## 🧪 Verification Phase (45-60 minutes)

### 🔌 System Connectivity Tests
- [ ] **Database Connection Tests** (15 min)
  - [ ] Test MongoDB connection
  - [ ] Test MySQL connection  
  - [ ] Test Pinecone connection
  - [ ] All connections successful (✅)

- [ ] **API Keys Validation** (5 min)
  - [ ] Test OpenAI API key validity
  - [ ] Verify API access working

### 🔄 End-to-End Functionality Validation
- [ ] **Complete Implementation Test** (20 min)
  - [ ] Run: `python test/test_complete_implementation.py`
  - [ ] MySQL Storage: ✅ PASSED
  - [ ] Web Console: ✅ PASSED
  - [ ] Data Consistency: ✅ PASSED

- [ ] **Web Console Test** (10 min)
  - [ ] Start server: `python web/qa_results_server.py`
  - [ ] Test health endpoint: http://localhost:5000/health
  - [ ] Test API endpoint: http://localhost:5000/api/qa-results
  - [ ] Verify data displays correctly

- [ ] **Parallel Processing Test** (10 min) - Optional if you have data
  - [ ] Test small batch: `python scripts/autoqa_parallel_processor.py --limit 3 --workers 2`
  - [ ] Verify successful processing
  - [ ] Check success rate: 100%

### ⚡ Performance Benchmarking
- [ ] **Database Performance** (5 min)
  - [ ] MySQL query time: < 0.1s
  - [ ] Pinecone query time: < 0.5s
  - [ ] Web console response: < 1s

---

## 📚 Documentation Phase (15-20 minutes)

### 📝 Update Project Documentation
- [ ] **Create Deployment Record** (10 min)
  - [ ] Document deployment date and machine
  - [ ] Record Python version and environment
  - [ ] Document database connection status
  - [ ] Note any configuration changes

- [ ] **Update System Documentation** (10 min)
  - [ ] Update AUTOQA_SYSTEM_DOCUMENTATION.md with deployment info
  - [ ] Document new machine specifications
  - [ ] Update any environment-specific instructions

---

## ✅ Post-Migration Verification (10-15 minutes)

### 🎯 Final Verification Steps
- [ ] **All database connections working**
- [ ] **Environment variables properly set**
- [ ] **Virtual environment activated and working**
- [ ] **All dependencies installed correctly**
- [ ] **Web console accessible and functional**
- [ ] **Parallel processing working** (if tested)
- [ ] **Performance benchmarks acceptable**
- [ ] **Documentation updated**
- [ ] **Backup procedures documented**

### 🏆 Success Criteria Verification
- [ ] **All tests pass**: `test_complete_implementation.py` shows all ✅ PASSED
- [ ] **Web console functional**: Loads and displays data correctly
- [ ] **Database queries work**: Return expected results
- [ ] **API endpoints responsive**: Respond within acceptable time limits
- [ ] **No error messages**: Clean logs during normal operation

---

## 🚨 Troubleshooting Quick Reference

### Common Issues and Solutions

#### ❌ Python Version Issues
- **Problem**: Python version < 3.9
- **Solution**: Install Python 3.9+ from python.org

#### ❌ Virtual Environment Issues  
- **Problem**: Environment not activating
- **Solution**: Delete and recreate: `rm -rf myenv && python -m venv myenv`

#### ❌ Package Installation Failures
- **Problem**: pip install errors
- **Solution**: `python -m pip install --upgrade pip setuptools wheel`

#### ❌ Database Connection Failures
- **MongoDB**: Check connection string format and network access
- **MySQL**: Verify service running and credentials correct
- **Pinecone**: Verify API key format (starts with 'pcsk_')

#### ❌ Environment Variables Not Loading
- **Problem**: Variables not found
- **Solution**: Check .env file format (no spaces around =)

---

## 🔄 Emergency Rollback Procedure

### If Migration Fails:
1. [ ] **Stop all AutoQA processes**
2. [ ] **Return to original machine/environment**
3. [ ] **Restore MySQL from backup** (if data was modified)
4. [ ] **Restore original configuration**: `cp .env.backup .env`
5. [ ] **Verify original system**: `python test/test_complete_implementation.py`

---

## 📞 Final Sign-off

### Deployment Completion
- [ ] **All checklist items completed**
- [ ] **All tests passing**
- [ ] **System fully operational**
- [ ] **Documentation updated**
- [ ] **Team notified of new deployment**

**Deployment Status**: ⬜ SUCCESS ⬜ FAILED ⬜ PARTIAL

**Final Notes**:
_________________________________________________
_________________________________________________
_________________________________________________

**Deployed By**: _____________________ **Date**: _________

**Verified By**: _____________________ **Date**: _________

---

## 📋 Next Steps After Successful Deployment

1. [ ] **Run small test batch** to verify parallel processing
2. [ ] **Set up monitoring** for new environment  
3. [ ] **Schedule regular backups** of MySQL data
4. [ ] **Document environment-specific configurations**
5. [ ] **Train team members** on new deployment location

---

**Checklist Version**: 1.0  
**Compatible with**: AutoQA v2.0+ (Production Ready)  
**Last Updated**: December 2024
